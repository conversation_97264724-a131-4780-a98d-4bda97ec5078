import { sentrySvelteKit } from '@sentry/sveltekit';
import { defineConfig } from 'vitest/config';
import { sveltekit } from '@sveltejs/kit/vite';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
	plugins: [
		sentrySvelteKit({
			sourceMapsUploadOptions: {
				org: 'aurora-construction-consultanc',
				project: 'cost-atlas-staging',
			},
		}),
		tailwindcss(),
		sveltekit(),
	],
	server: {
		allowedHosts: ['costatlas.lehrer.us'],
	},
	optimizeDeps: {
		exclude: ['chromium-bidi', 'fsevents'],
	},
});
