INSERT INTO
	"public"."wbs_library" ("name", "description")
VALUES
	(
		'Custom WBS',
		'Use this for a client- or project-specific WBS'
	);

INSERT INTO
	"public"."wbs_library" ("name", "description")
VALUES
	('ICMS v3', 'ICMS third edition, November 2021');

BEGIN;

-- Get the UUID for the ICMS v3 WBS library
DO $$
DECLARE
    icms_v3_library_id UUID;
BEGIN
    SELECT wbs_library_id INTO icms_v3_library_id 
    FROM public.wbs_library 
    WHERE name = 'ICMS v3';
    
    IF icms_v3_library_id IS NULL THEN
        RAISE EXCEPTION 'ICMS v3 WBS library not found. Please ensure it exists before running this seed.';
    END IF;

INSERT INTO
	public.wbs_library_item (
		wbs_library_item_id,
		parent_item_id,
		wbs_library_id,
		level,
		in_level_code,
		code,
		description,
		cost_scope,
		item_type
	)
VALUES
	(
		'c777ec64-2466-4d63-a315-2fb7a116403f',
		NULL,
		icms_v3_library_id,
		2,
		'01',
		'01',
		'Buildings',
		'A construction with a cover and enclosure to house people, equipment or goods.',
		'Standard'
	),
	(
		'5ad29419-5aed-4c29-9d16-cfb9acbf059d',
		'c777ec64-2466-4d63-a315-2fb7a116403f',
		icms_v3_library_id,
		2,
		'01',
		'01.01',
		'Acquisition Costs (AC)',
		'All payments or considerations required to acquire/lease/purchase the land, property or existing Constructed Asset, and all other expenses associated with the acquisition, excluding physical construction.',
		'Standard'
	),
	(
		'd5f39d4f-b331-475f-81bf-6952a0783444',
		'5ad29419-5aed-4c29-9d16-cfb9acbf059d',
		icms_v3_library_id,
		3,
		'01',
		'01.01.01',
		'Site acquisition',
		'All payments required to acquire the site, excluding physical construction.',
		'Standard'
	),
	(
		'2a1cafe0-9110-45cd-9313-c8c1f1935dbe',
		'd5f39d4f-b331-475f-81bf-6952a0783444',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Costs and premium required to procure site',
		NULL,
		'Standard'
	),
	(
		'6b486926-910a-44ee-8cef-ce2bc7ce3b92',
		'd5f39d4f-b331-475f-81bf-6952a0783444',
		icms_v3_library_id,
		4,
		'020',
		'01.01.01.020',
		'Compensation to existing occupiers',
		NULL,
		'Standard'
	),
	(
		'8e191fc4-6bc5-44a3-975c-36e054772e81',
		'd5f39d4f-b331-475f-81bf-6952a0783444',
		icms_v3_library_id,
		4,
		'030',
		'01.01.01.030',
		'Demolition, removal and modification of existing properties by way of payment to existing owners instead of carrying out physical work',
		NULL,
		'Standard'
	),
	(
		'78966c1e-730a-4f4a-832d-3a5fb1b821ea',
		'd5f39d4f-b331-475f-81bf-6952a0783444',
		icms_v3_library_id,
		4,
		'040',
		'01.01.01.040',
		'Contributions for the preservation of heritage, culture and environment',
		NULL,
		'Standard'
	),
	(
		'56aa4830-dfeb-45a1-bfaa-ca2f52112e98',
		'd5f39d4f-b331-475f-81bf-6952a0783444',
		icms_v3_library_id,
		4,
		'050',
		'01.01.01.050',
		'Related fees to agents, lawyers, and the like',
		NULL,
		'Standard'
	),
	(
		'2b84a492-dee4-4311-8300-8535ba96378b',
		'd5f39d4f-b331-475f-81bf-6952a0783444',
		icms_v3_library_id,
		4,
		'060',
		'01.01.01.060',
		'Related taxes and statutory charges',
		NULL,
		'Standard'
	),
	(
		'1225aaf7-6952-466a-96e0-735f64d2cc6f',
		'5ad29419-5aed-4c29-9d16-cfb9acbf059d',
		icms_v3_library_id,
		3,
		'02',
		'01.01.02',
		'Administrative',
		' financial',
		'Standard'
	),
	(
		'c6522258-a185-4efd-8754-7933bee36cd0',
		'1225aaf7-6952-466a-96e0-735f64d2cc6f',
		icms_v3_library_id,
		4,
		'010',
		'01.01.02.010',
		'Client’s general office overheads',
		NULL,
		'Standard'
	),
	(
		'1b67bd3b-81b7-40c4-b0f8-9c1c2c5e6e32',
		'1225aaf7-6952-466a-96e0-735f64d2cc6f',
		icms_v3_library_id,
		4,
		'020',
		'01.01.02.020',
		'Client’s project-specific administrative expenses',
		NULL,
		'Standard'
	),
	(
		'3fc07e95-916a-4ce4-a531-7fae3bfaf30d',
		'1225aaf7-6952-466a-96e0-735f64d2cc6f',
		icms_v3_library_id,
		4,
		'030',
		'01.01.02.030',
		'Interest and finance costs',
		NULL,
		'Standard'
	),
	(
		'3dbb7732-0eaa-4914-9dfe-87acc9ca6ddd',
		'1225aaf7-6952-466a-96e0-735f64d2cc6f',
		icms_v3_library_id,
		4,
		'040',
		'01.01.02.040',
		'Legal expenses',
		NULL,
		'Standard'
	),
	(
		'b35992cf-fb7a-460f-9fe2-7f78f6e7a876',
		'1225aaf7-6952-466a-96e0-735f64d2cc6f',
		icms_v3_library_id,
		4,
		'050',
		'01.01.02.050',
		'Accounting expenses',
		NULL,
		'Standard'
	),
	(
		'e78e1ebe-43cb-4d1e-990a-66cabcca8e86',
		'1225aaf7-6952-466a-96e0-735f64d2cc6f',
		icms_v3_library_id,
		4,
		'060',
		'01.01.02.060',
		'Sales, leasing, marketing, advertising and promotional expenses',
		NULL,
		'Standard'
	),
	(
		'840d6b12-39be-4626-8037-cca89b92f4b1',
		'1225aaf7-6952-466a-96e0-735f64d2cc6f',
		icms_v3_library_id,
		4,
		'070',
		'01.01.02.070',
		'Taxes and statutory charges related to sales and lease',
		NULL,
		'Standard'
	),
	(
		'f37f21f3-6c7f-4c37-b8da-9e0d087b99ca',
		'1225aaf7-6952-466a-96e0-735f64d2cc6f',
		icms_v3_library_id,
		4,
		'080',
		'************',
		'License and permit charges for operation and use',
		NULL,
		'Standard'
	),
	(
		'5345ef96-a4e8-443b-b581-778847d79680',
		'c777ec64-2466-4d63-a315-2fb7a116403f',
		icms_v3_library_id,
		2,
		'02',
		'01.02',
		'Construction Costs (CC)',
		'Expenditures incurred as a direct result of construction including labour, materials, plant, equipment, site and head office overheads and profits as well as taxes and levies. They are the total price payable for all permanent and temporary works normally included in construction contracts, including goods or materials supplied by the Client for the Constructor to install.',
		'Standard'
	),
	(
		'6862e237-7ef5-4c16-b948-539dde380809',
		'5345ef96-a4e8-443b-b581-778847d79680',
		icms_v3_library_id,
		3,
		'01',
		'01.02.01',
		'Demolition',
		' site preparation and formation',
		'Standard'
	),
	(
		'2a32be22-0371-4b2a-836b-35a3a07e89a2',
		'6862e237-7ef5-4c16-b948-539dde380809',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Site survey and ground investigation',
		NULL,
		'Standard'
	),
	(
		'd9d5232d-743f-4247-884b-353ac638a275',
		'6862e237-7ef5-4c16-b948-539dde380809',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Environmental treatment',
		NULL,
		'Standard'
	),
	(
		'ce490aac-2087-4d6d-a19e-43772465c020',
		'6862e237-7ef5-4c16-b948-539dde380809',
		icms_v3_library_id,
		4,
		'030',
		'01.02.01.030',
		'Sampling of hazardous or useful materials or conditions',
		NULL,
		'Standard'
	),
	(
		'ccde3472-8cd4-4272-90d1-21ed200a64f6',
		'6862e237-7ef5-4c16-b948-539dde380809',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Temporary fencing',
		NULL,
		'Standard'
	),
	(
		'fc75043f-fc77-423a-8382-6a83c3e357ea',
		'6862e237-7ef5-4c16-b948-539dde380809',
		icms_v3_library_id,
		4,
		'050',
		'************',
		'Demolition of existing buildings and support to adjacent structures',
		NULL,
		'Standard'
	),
	(
		'891fe245-4c47-4864-9933-ca4ed52b9d7e',
		'6862e237-7ef5-4c16-b948-539dde380809',
		icms_v3_library_id,
		4,
		'060',
		'************',
		'Site surface clearance (clearing, grubbing, topsoil stripping, tree felling, minor earthwork, removal)',
		NULL,
		'Standard'
	),
	(
		'b1040d25-8dee-4c08-b1c9-05bc80018a25',
		'6862e237-7ef5-4c16-b948-539dde380809',
		icms_v3_library_id,
		4,
		'070',
		'************',
		'Tree transplant',
		NULL,
		'Standard'
	),
	(
		'2820e9b9-ae0e-4956-9f82-532141d6dfb9',
		'6862e237-7ef5-4c16-b948-539dde380809',
		icms_v3_library_id,
		4,
		'080',
		'************',
		'Site formation and slope treatment',
		NULL,
		'Standard'
	),
	(
		'134e31ee-5a90-4d58-969b-4ff25e4884de',
		'6862e237-7ef5-4c16-b948-539dde380809',
		icms_v3_library_id,
		4,
		'090',
		'************',
		'Temporary surface drainage and dewatering',
		NULL,
		'Standard'
	),
	(
		'f861e549-2d70-4a71-a712-d57e9c1198e8',
		'6862e237-7ef5-4c16-b948-539dde380809',
		icms_v3_library_id,
		4,
		'100',
		'************',
		'Temporary protection, diversion and relocation of public utilities',
		NULL,
		'Standard'
	),
	(
		'c3e1b3c0-3e62-479c-accc-0b8a67e3e3f6',
		'6862e237-7ef5-4c16-b948-539dde380809',
		icms_v3_library_id,
		4,
		'110',
		'************',
		'Erosion control',
		NULL,
		'Standard'
	),
	(
		'2504f4c9-8ce6-4d78-b903-3022371f911f',
		'5345ef96-a4e8-443b-b581-778847d79680',
		icms_v3_library_id,
		3,
		'02',
		'01.02.02',
		'Substructure',
		'All the load bearing work underground or underwater up to and including the following (including related earthwork, lateral support beyond site formation, and non-load bearing components and services and equipment forming an integral part of composite or prefabricated load bearing work) and as illustrated in Part 4.2: for buildings: lowest floor slabs, and basement sides and bottom including related waterproofing and insulation',
		'Standard'
	),
	(
		'74903293-8923-4445-8533-cfcfb659879b',
		'2504f4c9-8ce6-4d78-b903-3022371f911f',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Foundation piling and underpinning',
		NULL,
		'Standard'
	),
	(
		'280a72fb-a48e-4abb-aad5-2836696ebfc5',
		'2504f4c9-8ce6-4d78-b903-3022371f911f',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Foundations up to top of lowest floor slabs',
		NULL,
		'Standard'
	),
	(
		'7bd82805-d797-45c9-b7de-450a11ba5fba',
		'2504f4c9-8ce6-4d78-b903-3022371f911f',
		icms_v3_library_id,
		4,
		'030',
		'************',
		'Basement sides and bottom',
		NULL,
		'Standard'
	),
	(
		'd65572cc-8e9f-404d-9dbf-fefa3e5bb4fc',
		'5345ef96-a4e8-443b-b581-778847d79680',
		icms_v3_library_id,
		3,
		'03',
		'01.02.03',
		'Structure',
		'All the load bearing work, including non-load bearing components and services and equipment forming an integral part of composite or prefabricated load bearing work, excluding those included in Substructure and Architectural works | Non-structural works.',
		'Standard'
	),
	(
		'6a2af835-95d5-4eb3-9764-7ef6166439c6',
		'd65572cc-8e9f-404d-9dbf-fefa3e5bb4fc',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Structural removal and alterations',
		NULL,
		'Standard'
	),
	(
		'd7de5e50-701b-4a61-bd43-09e6376625c1',
		'd65572cc-8e9f-404d-9dbf-fefa3e5bb4fc',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Basement suspended floors (up to top of ground floor slabs)',
		NULL,
		'Standard'
	),
	(
		'8c0d4f4e-250f-4a7c-b4f9-dbc7eece2b31',
		'd65572cc-8e9f-404d-9dbf-fefa3e5bb4fc',
		icms_v3_library_id,
		4,
		'030',
		'************',
		'Frames and slabs (above top of ground floor slabs)',
		NULL,
		'Standard'
	),
	(
		'6cf3e5bc-ae49-46e2-8cc8-02cd94b66875',
		'd65572cc-8e9f-404d-9dbf-fefa3e5bb4fc',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Tanks, pools, sundries',
		NULL,
		'Standard'
	),
	(
		'e614c13d-aff3-485e-9da2-e7d37b877ab5',
		'd65572cc-8e9f-404d-9dbf-fefa3e5bb4fc',
		icms_v3_library_id,
		4,
		'050',
		'************',
		'Composite or prefabricated work',
		NULL,
		'Standard'
	),
	(
		'40c109b9-5403-435e-a2d7-1c8866517394',
		'5345ef96-a4e8-443b-b581-778847d79680',
		icms_v3_library_id,
		3,
		'04',
		'01.02.04',
		'Architectural works | Non-structural works',
		'All architectural and non-load bearing work excluding services, equipment, and surface and underground drainage.',
		'Standard'
	),
	(
		'628bc7ca-a5bb-480f-a1b7-f39b557d797a',
		'40c109b9-5403-435e-a2d7-1c8866517394',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Non-structural removal and alterations',
		NULL,
		'Standard'
	),
	(
		'040e46c6-b3ab-4159-a684-76fd5bc81e66',
		'40c109b9-5403-435e-a2d7-1c8866517394',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'External elevations',
		NULL,
		'Standard'
	),
	(
		'9917ab30-e519-41ad-80cb-a591ba21fcb5',
		'40c109b9-5403-435e-a2d7-1c8866517394',
		icms_v3_library_id,
		4,
		'030',
		'************',
		'Roof finishes, skylights and landscaping (including waterproofing and insulation)',
		NULL,
		'Standard'
	),
	(
		'76f7049f-7f60-4552-87c5-8d12f73b8814',
		'40c109b9-5403-435e-a2d7-1c8866517394',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Internal divisions',
		NULL,
		'Standard'
	),
	(
		'0c9cc728-6927-411a-b240-bd72029a5c35',
		'40c109b9-5403-435e-a2d7-1c8866517394',
		icms_v3_library_id,
		4,
		'050',
		'01.02.04.050',
		'Fittings and sundries',
		NULL,
		'Standard'
	),
	(
		'ffbf3530-9d30-47ef-b2f7-7b5792d1827d',
		'40c109b9-5403-435e-a2d7-1c8866517394',
		icms_v3_library_id,
		4,
		'060',
		'01.02.04.060',
		'Finishes under cover',
		NULL,
		'Standard'
	),
	(
		'071bbf72-b467-44cb-be0d-0755409f4ec3',
		'40c109b9-5403-435e-a2d7-1c8866517394',
		icms_v3_library_id,
		4,
		'070',
		'************',
		'Builder’s work in connection with services',
		NULL,
		'Standard'
	),
	(
		'799fbaf3-a659-410a-9923-d3e4b1d25c1c',
		'40c109b9-5403-435e-a2d7-1c8866517394',
		icms_v3_library_id,
		4,
		'080',
		'************',
		'Composite or prefabricated work',
		NULL,
		'Standard'
	),
	(
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		'5345ef96-a4e8-443b-b581-778847d79680',
		icms_v3_library_id,
		3,
		'05',
		'01.02.05',
		'Services and equipment',
		'All fixed services and equipment required [to put the completed project into use for Construction Costs | to sustain the use after completion of construction for Renewal and Maintenance Costs], whether they are mechanical, hydraulic, plumbing, fire-fighting, transport, communication, security, electrical or electronic, control systems, or signalling excluding external surface and underground drainage. Including testing, commissioning and operational licensing and plant upgrades/refurbishment.',
		'Standard'
	),
	(
		'94c95e02-8283-4f5d-9ba1-dac3f6b23966',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Heating, ventilating and air-conditioning systems/air conditioners',
		NULL,
		'Standard'
	),
	(
		'62022722-d351-4aee-98dd-9abd0b0eafa6',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Electrical services',
		NULL,
		'Standard'
	),
	(
		'87f1a8b7-0e76-480e-b14e-c54e79d4007d',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'030',
		'01.02.05.030',
		'Fitting out lighting fittings',
		NULL,
		'Standard'
	),
	(
		'af91f359-46ff-4ed7-b163-22928f3a56e8',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'040',
		'01.02.05.040',
		'Extra low voltage electrical services',
		NULL,
		'Standard'
	),
	(
		'712c04b5-0194-4d75-949b-d17e85aef652',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'050',
		'01.02.05.050',
		'Water supply and drainage above ground or inside basement',
		NULL,
		'Standard'
	),
	(
		'3ebe215b-4dba-4c47-a4f3-a1c73f593eb0',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'060',
		'01.02.05.060',
		'Supply of sanitary fittings and fixtures',
		NULL,
		'Standard'
	),
	(
		'ddefc6c9-c3a0-41b5-a43c-bd6445f8c600',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'070',
		'01.02.05.070',
		'Disposal systems',
		NULL,
		'Standard'
	),
	(
		'2061d7ab-98ae-4962-9c94-8533ba96e441',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'080',
		'01.02.05.080',
		'Fire services',
		NULL,
		'Standard'
	),
	(
		'b57dbd72-23c8-4234-9e06-11c3f6efde03',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'090',
		'01.02.05.090',
		'Gas services',
		NULL,
		'Standard'
	),
	(
		'af66edfa-88da-449f-b858-af874992b710',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'100',
		'01.02.05.100',
		'Movement systems',
		NULL,
		'Standard'
	),
	(
		'78bf5cf5-af22-4edc-b9db-cd5dfbb29d76',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'110',
		'01.02.05.110',
		'Gondolas',
		NULL,
		'Standard'
	),
	(
		'1d211921-93a0-4d5e-ad9f-8e8acf74f11e',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'120',
		'01.02.05.120',
		'Turntables',
		NULL,
		'Standard'
	),
	(
		'6e7cb57d-4e1a-4832-a99f-914499b97895',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'130',
		'************',
		'Generators',
		NULL,
		'Standard'
	),
	(
		'3b19a88e-2ee8-4977-8eb1-d08ef5cf32dd',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'140',
		'************',
		'Energy-saving features',
		NULL,
		'Standard'
	),
	(
		'9890bf58-1566-4be2-a449-65b10c261e3f',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'150',
		'************',
		'Water and wastewater treatment equipment',
		NULL,
		'Standard'
	),
	(
		'22a20736-b5a0-4dbc-a3d9-3c74be15aefc',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'160',
		'************',
		'Fountains, pools and filtration plant',
		NULL,
		'Standard'
	),
	(
		'6fd96364-7f26-43b4-afa1-3e7d2118a369',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'170',
		'************',
		'Powered building signage',
		NULL,
		'Standard'
	),
	(
		'fae03a21-bfff-49bb-92f3-9b468baf8680',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'175',
		'************',
		'Audio/visual entertainment system',
		NULL,
		'Standard'
	),
	(
		'97c1e4b4-b19c-4cd8-b682-255d7ac53376',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'180',
		'************',
		'Kitchen equipment',
		NULL,
		'Standard'
	),
	(
		'4b32fc10-704d-48ea-bdfe-3a8a2f067fd6',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'190',
		'************',
		'Cold room equipment',
		NULL,
		'Standard'
	),
	(
		'a3417e1a-334f-4db2-8d9e-f338b6a760fc',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'200',
		'************',
		'Laboratory equipment',
		NULL,
		'Standard'
	),
	(
		'aa0a126b-f1b0-446a-a1f2-e3c7323d91e4',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'210',
		'************',
		'Medical equipment',
		NULL,
		'Standard'
	),
	(
		'fe681459-99f6-4fc1-a433-42505f634639',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'220',
		'************',
		'Hotel equipment',
		NULL,
		'Standard'
	),
	(
		'c0737fef-5d56-4303-ab38-2fd034d653b8',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'230',
		'************',
		'Car park or entrances access control',
		NULL,
		'Standard'
	),
	(
		'85929479-06e3-4c19-ad64-1570c77775cc',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'240',
		'************',
		'Domestic appliances',
		NULL,
		'Standard'
	),
	(
		'505b54bf-4775-4575-a794-a3f2bf7ea524',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'250',
		'************',
		'Other specialist services',
		NULL,
		'Standard'
	),
	(
		'e7241cd6-7a8c-4364-9b24-8d90772347ff',
		'37c7ffb2-ddfe-456f-a98b-31acde7d73b0',
		icms_v3_library_id,
		4,
		'260',
		'01.02.05.260',
		'Builder’s profit and attendance on services',
		NULL,
		'Standard'
	),
	(
		'0dca89d1-6e92-4792-a4e0-b1e3dd4ccba2',
		'5345ef96-a4e8-443b-b581-778847d79680',
		icms_v3_library_id,
		3,
		'06',
		'01.02.06',
		'Surface and underground drainage',
		'All underground or external surface drainage systems excluding those inside basement or underground construction.',
		'Standard'
	),
	(
		'575a3bea-9da9-47fa-b116-c78bb9ab2f7a',
		'0dca89d1-6e92-4792-a4e0-b1e3dd4ccba2',
		icms_v3_library_id,
		4,
		'010',
		'01.02.06.010',
		'Surface water drainage',
		NULL,
		'Standard'
	),
	(
		'9bfce172-aeaa-4025-a350-4b0082b6e9b9',
		'0dca89d1-6e92-4792-a4e0-b1e3dd4ccba2',
		icms_v3_library_id,
		4,
		'020',
		'01.02.06.020',
		'Storm water drainage',
		NULL,
		'Standard'
	),
	(
		'53fc885c-7667-4b8b-8489-425d14da5bf6',
		'0dca89d1-6e92-4792-a4e0-b1e3dd4ccba2',
		icms_v3_library_id,
		4,
		'030',
		'01.02.06.030',
		'Foul and wastewater drainage',
		NULL,
		'Standard'
	),
	(
		'6d13b128-ba28-4fa6-a2a3-be2eaa5c84b0',
		'0dca89d1-6e92-4792-a4e0-b1e3dd4ccba2',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Drainage disconnections and connections',
		NULL,
		'Standard'
	),
	(
		'dbbbea8e-3912-4102-8576-ddc54305d7de',
		'0dca89d1-6e92-4792-a4e0-b1e3dd4ccba2',
		icms_v3_library_id,
		4,
		'050',
		'************',
		'CCTV inspection of existing or new drains',
		NULL,
		'Standard'
	),
	(
		'72e57778-49e4-4767-8b91-104b6a7a6796',
		'0dca89d1-6e92-4792-a4e0-b1e3dd4ccba2',
		icms_v3_library_id,
		4,
		'060',
		'************',
		'Buried Process Pipe',
		NULL,
		'Standard'
	),
	(
		'5f8dd5d0-23f2-43be-8bcc-6a057bb82e04',
		'5345ef96-a4e8-443b-b581-778847d79680',
		icms_v3_library_id,
		3,
		'07',
		'01.02.07',
		'External and ancillary works',
		'All work outside the external face of buildings or beyond the construction entity required to fulfil the primary function of the Project and not included in other Groups.',
		'Standard'
	),
	(
		'a21e9212-7f47-4305-9072-d68022d96464',
		'5f8dd5d0-23f2-43be-8bcc-6a057bb82e04',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Permanent retaining structures',
		NULL,
		'Standard'
	),
	(
		'7be92cba-2ba1-4941-af12-418f7f2ce18f',
		'5f8dd5d0-23f2-43be-8bcc-6a057bb82e04',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Site enclosures and divisions',
		NULL,
		'Standard'
	),
	(
		'40f76440-5091-4fe2-ad8b-ef6ebd0b4bb2',
		'5f8dd5d0-23f2-43be-8bcc-6a057bb82e04',
		icms_v3_library_id,
		4,
		'030',
		'************',
		'Ancillary structures',
		NULL,
		'Standard'
	),
	(
		'8275bc0b-5615-4c52-8cfc-07e8bcff1d6c',
		'5f8dd5d0-23f2-43be-8bcc-6a057bb82e04',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Roads and paving',
		NULL,
		'Standard'
	),
	(
		'e95f26af-7d81-4008-b380-722d0025aa70',
		'5f8dd5d0-23f2-43be-8bcc-6a057bb82e04',
		icms_v3_library_id,
		4,
		'050',
		'************',
		'Landscaping (hard and soft)',
		NULL,
		'Standard'
	),
	(
		'fdcc790f-7938-47bb-997f-dc31431210ce',
		'5f8dd5d0-23f2-43be-8bcc-6a057bb82e04',
		icms_v3_library_id,
		4,
		'060',
		'************',
		'Fittings and equipment',
		NULL,
		'Standard'
	),
	(
		'd2443fc6-7200-4fed-a225-1da37d96f0c3',
		'5f8dd5d0-23f2-43be-8bcc-6a057bb82e04',
		icms_v3_library_id,
		4,
		'070',
		'************',
		'External services',
		NULL,
		'Standard'
	),
	(
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		'5345ef96-a4e8-443b-b581-778847d79680',
		icms_v3_library_id,
		3,
		'08',
		'01.02.08',
		'Preliminaries | Constructors’ site overheads | general requirements',
		'Constructors’ site management, temporary site facilities, site services, mobilisation, demobilisation and other expenses, not directly related to a particular Group, but commonly required to be shared by all Groups.',
		'Standard'
	),
	(
		'6ac536a3-fb57-45a7-8d35-dfbcdd58f2fb',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Construction management including site management staff and support labour',
		NULL,
		'Standard'
	),
	(
		'9ca6bdff-b7a9-4b0e-936a-892aee368a2c',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'020',
		'01.02.08.020',
		'Temporary access roads and storage areas, traffic management and diversion (at the Constructors’ discretion)',
		NULL,
		'Standard'
	),
	(
		'93443420-9348-454a-8da3-543cbf3cf4e5',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'030',
		'01.02.08.030',
		'Temporary site fencing and securities',
		NULL,
		'Standard'
	),
	(
		'c9037d13-cdb2-46ab-af1b-c5af19e967d7',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'040',
		'01.02.08.040',
		'Commonly shared construction plant',
		NULL,
		'Standard'
	),
	(
		'1c3978c8-75d8-47cf-a16e-08c4d5387f95',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'050',
		'01.02.08.050',
		'Commonly shared scaffolding',
		NULL,
		'Standard'
	),
	(
		'dd197c8a-fd36-4a14-9e57-a97f28fc0e4a',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'060',
		'01.02.08.060',
		'Other temporary facilities and services',
		NULL,
		'Standard'
	),
	(
		'0801a826-f6db-4c38-8fcd-68ea11380bda',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'070',
		'01.02.08.070',
		'Technology and communications: telephone, broadband, hardware, software',
		NULL,
		'Standard'
	),
	(
		'0bc90c3b-3175-4ca6-8f9d-27d4ac24fe54',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'080',
		'01.02.08.080',
		'Constructor’s submissions, reports and as-built documentation',
		NULL,
		'Standard'
	),
	(
		'3f19d949-14c1-42c3-8f8e-8dc39d5bc113',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'090',
		'01.02.08.090',
		'Quality monitoring, recording and inspections',
		NULL,
		'Standard'
	),
	(
		'e2737896-156d-454f-9541-81c2c4a97cb9',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'100',
		'01.02.08.100',
		'Safety, health and environmental management',
		NULL,
		'Standard'
	),
	(
		'b4424851-a894-4565-95c1-efcd554cf731',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'110',
		'01.02.08.110',
		'Insurances, bonds, guarantees and warranties',
		NULL,
		'Standard'
	),
	(
		'f91ea550-1939-4874-a58e-63c61ce4724d',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'120',
		'01.02.08.120',
		'Constructor’s statutory fees and charges',
		NULL,
		'Standard'
	),
	(
		'1d310b6e-28ee-4158-b7aa-3efc702a808d',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'130',
		'01.02.08.130',
		'Testing and commissioning',
		NULL,
		'Standard'
	),
	(
		'fe050c1c-94c5-47b9-b330-65363e6f2bae',
		'6901e459-950d-43b9-b63d-4f80ba3baeac',
		icms_v3_library_id,
		4,
		'140',
		'01.02.08.140',
		'Extras for extreme climatic or working conditions (if priced separately according to local pricing practice)',
		NULL,
		'Standard'
	),
	(
		'43594e47-2bbf-4fc1-92f9-105ee13eba22',
		'5345ef96-a4e8-443b-b581-778847d79680',
		icms_v3_library_id,
		3,
		'09',
		'01.02.09',
		'Risk Allowances',
		'As defined in section 4.1 but related to [Construction | Renewal | Maintenance] Costs and not included in other Groups.',
		'Standard'
	),
	(
		'149d0419-651a-491d-874d-a9796595ef38',
		'43594e47-2bbf-4fc1-92f9-105ee13eba22',
		icms_v3_library_id,
		4,
		'010',
		'01.02.09.010',
		'Design development allowance',
		NULL,
		'Standard'
	),
	(
		'f29448fb-480f-4b5c-af28-064b3d45f573',
		'43594e47-2bbf-4fc1-92f9-105ee13eba22',
		icms_v3_library_id,
		4,
		'020',
		'01.02.09.020',
		'Construction contingencies',
		NULL,
		'Standard'
	),
	(
		'3100a289-5e84-4134-9653-5c931f5220a3',
		'43594e47-2bbf-4fc1-92f9-105ee13eba22',
		icms_v3_library_id,
		4,
		'030',
		'01.02.09.030',
		'Price Level Adjustments',
		NULL,
		'Standard'
	),
	(
		'9923c8d8-8100-4f3a-a298-0c03a4e1e536',
		'43594e47-2bbf-4fc1-92f9-105ee13eba22',
		icms_v3_library_id,
		4,
		'040',
		'01.02.09.040',
		'Exchange rate fluctuation adjustments',
		NULL,
		'Standard'
	),
	(
		'b1765c45-8c46-4234-96b8-933a21cabc17',
		'5345ef96-a4e8-443b-b581-778847d79680',
		icms_v3_library_id,
		3,
		'10',
		'01.02.10',
		'Taxes and Levies',
		'As defined in section 4.1 and not included in other Groups.',
		'Standard'
	),
	(
		'bc85eec3-ca09-45a4-a384-735c6f76aa85',
		'b1765c45-8c46-4234-96b8-933a21cabc17',
		icms_v3_library_id,
		4,
		'010',
		'01.02.10.010',
		'Paid by the Constructor',
		NULL,
		'Standard'
	),
	(
		'5917bce9-3590-4150-9d8c-d14815df19fe',
		'b1765c45-8c46-4234-96b8-933a21cabc17',
		icms_v3_library_id,
		4,
		'020',
		'01.02.10.020',
		'Paid by the Client in relation to the construction contract payments',
		NULL,
		'Standard'
	),
	(
		'e804277b-04df-46a0-ae2d-a28cf1c506f8',
		'5345ef96-a4e8-443b-b581-778847d79680',
		icms_v3_library_id,
		3,
		'11',
		'01.02.11',
		'Work and utilities off-site',
		'All payments to government authorities or public utility companies to connect | keep connected public work and utilities to the site, or services diversions, to enable the Project, including related risk allowances, taxes and levies.',
		'Standard'
	),
	(
		'dbacccdc-412e-4a52-b06a-279c85e9dcfe',
		'e804277b-04df-46a0-ae2d-a28cf1c506f8',
		icms_v3_library_id,
		4,
		'010',
		'01.02.11.010',
		'Connections to, diversion of and capacity enhancement of public utility mains or sources off-site up to mains connections on-site',
		NULL,
		'Standard'
	),
	(
		'6137cb3c-838c-451f-af97-aa979722d869',
		'e804277b-04df-46a0-ae2d-a28cf1c506f8',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Public access roads and footpaths',
		NULL,
		'Standard'
	),
	(
		'd621df7d-6269-43ca-a0ae-ea4b0fb8173b',
		'5345ef96-a4e8-443b-b581-778847d79680',
		icms_v3_library_id,
		3,
		'12',
		'01.02.12',
		'Production and loose furniture',
		' fittings and equipment',
		'Standard'
	),
	(
		'faf5ec5b-3a18-4997-9ed1-7d90a6cb3e56',
		'd621df7d-6269-43ca-a0ae-ea4b0fb8173b',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Loose production, process and operating furniture, fittings and equipment not normally provided before completion of construction',
		NULL,
		'Standard'
	),
	(
		'ccf13585-a5ec-4c67-a8c2-51155f4049a0',
		'd621df7d-6269-43ca-a0ae-ea4b0fb8173b',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Fixed production, process and operating furniture, fittings and equipment installed before completion of construction',
		NULL,
		'Standard'
	),
	(
		'109c3534-0ab8-4227-8342-77fc12bc2113',
		'5345ef96-a4e8-443b-b581-778847d79680',
		icms_v3_library_id,
		3,
		'13',
		'01.02.13',
		'Construction-related consultants and supervision',
		'Fees and charges payable to Service Providers not engaged by the Constructors, including related risk allowances, taxes and levies.',
		'Standard'
	),
	(
		'0702d71f-6ac1-416c-bf39-a4e1ea51f925',
		'109c3534-0ab8-4227-8342-77fc12bc2113',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Consultants’ fees and reimbursable',
		NULL,
		'Standard'
	),
	(
		'12021182-7cce-4163-ae08-47e5409311bc',
		'109c3534-0ab8-4227-8342-77fc12bc2113',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Charges and levies payable to statutory bodies or their appointed agencies',
		NULL,
		'Standard'
	),
	(
		'5e4c7df3-9b90-45b0-a005-51ca94ebdbc1',
		'109c3534-0ab8-4227-8342-77fc12bc2113',
		icms_v3_library_id,
		4,
		'030',
		'01.02.13.030',
		'Site supervision charges (including their accommodation and travels)',
		NULL,
		'Standard'
	),
	(
		'72447c16-3215-469d-91c5-e136b19ea06a',
		'109c3534-0ab8-4227-8342-77fc12bc2113',
		icms_v3_library_id,
		4,
		'040',
		'01.02.13.040',
		'Payments to testing authorities or laboratories',
		NULL,
		'Standard'
	),
	(
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		'c777ec64-2466-4d63-a315-2fb7a116403f',
		icms_v3_library_id,
		2,
		'03',
		'01.03',
		'Renewal Costs (RC)',
		'The costs of replacing a Constructed Asset and/or major components once they reach the end of their life, and which the Client decides are to be included in the capital rather than the revenue budget.',
		'Standard'
	),
	(
		'085f97eb-4565-4127-99ce-5e3f17ff4fe9',
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		icms_v3_library_id,
		3,
		'01',
		'01.03.01',
		'Demolition',
		' site preparation and formation',
		'Standard'
	),
	(
		'6e1d5185-f836-4280-9199-4c2f28d2f597',
		'085f97eb-4565-4127-99ce-5e3f17ff4fe9',
		icms_v3_library_id,
		4,
		'010',
		'01.03.01.010',
		'Site survey and ground investigation',
		NULL,
		'Standard'
	),
	(
		'b551414f-87b6-4680-8783-6d7248f1f0fa',
		'085f97eb-4565-4127-99ce-5e3f17ff4fe9',
		icms_v3_library_id,
		4,
		'020',
		'01.03.01.020',
		'Environmental treatment',
		NULL,
		'Standard'
	),
	(
		'21150436-5c41-429b-b0fc-6a7490b0d6c9',
		'085f97eb-4565-4127-99ce-5e3f17ff4fe9',
		icms_v3_library_id,
		4,
		'030',
		'01.03.01.030',
		'Sampling of hazardous or useful materials or conditions',
		NULL,
		'Standard'
	),
	(
		'bc8ad1f5-bee9-47a9-a589-40a92ee06ba7',
		'085f97eb-4565-4127-99ce-5e3f17ff4fe9',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Temporary fencing',
		NULL,
		'Standard'
	),
	(
		'115dbe40-b2aa-4528-a2fb-fd80022e60ac',
		'085f97eb-4565-4127-99ce-5e3f17ff4fe9',
		icms_v3_library_id,
		4,
		'050',
		'************',
		'Demolition of existing buildings and support to adjacent structures',
		NULL,
		'Standard'
	),
	(
		'cfab6ba9-7121-4267-aefd-3d1448bb8d3a',
		'085f97eb-4565-4127-99ce-5e3f17ff4fe9',
		icms_v3_library_id,
		4,
		'060',
		'************',
		'Site surface clearance (clearing, grubbing, topsoil stripping, tree felling, minor earthwork, removal)',
		NULL,
		'Standard'
	),
	(
		'ff3e8a2c-fc5c-451d-b3bf-6ae0bff03832',
		'085f97eb-4565-4127-99ce-5e3f17ff4fe9',
		icms_v3_library_id,
		4,
		'070',
		'************',
		'Tree transplant',
		NULL,
		'Standard'
	),
	(
		'8dda7c09-a08a-479f-a094-fef3bf975197',
		'085f97eb-4565-4127-99ce-5e3f17ff4fe9',
		icms_v3_library_id,
		4,
		'080',
		'************',
		'Site formation and slope treatment',
		NULL,
		'Standard'
	),
	(
		'a0a81b8f-5cfe-466c-846a-059cb68903c5',
		'085f97eb-4565-4127-99ce-5e3f17ff4fe9',
		icms_v3_library_id,
		4,
		'090',
		'************',
		'Temporary surface drainage and dewatering',
		NULL,
		'Standard'
	),
	(
		'0220039c-145d-4afa-8662-11dc5e8ef250',
		'085f97eb-4565-4127-99ce-5e3f17ff4fe9',
		icms_v3_library_id,
		4,
		'100',
		'************',
		'Temporary protection, diversion and relocation of public utilities',
		NULL,
		'Standard'
	),
	(
		'8e279ada-3f6f-4be4-8276-822f410e5649',
		'085f97eb-4565-4127-99ce-5e3f17ff4fe9',
		icms_v3_library_id,
		4,
		'110',
		'************',
		'Erosion control',
		NULL,
		'Standard'
	),
	(
		'f947d0e1-1dff-4951-bab1-4a49e16333d5',
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		icms_v3_library_id,
		3,
		'02',
		'01.03.02',
		'Substructure',
		'All the load bearing work underground or underwater up to and including the following (including related earthwork, lateral support beyond site formation, and non-load bearing components and services and equipment forming an integral part of composite or prefabricated load bearing work) and as illustrated in Part 4.2: for buildings: lowest floor slabs, and basement sides and bottom including related waterproofing and insulation',
		'Standard'
	),
	(
		'f223f019-f321-4589-98cf-aa3da1de7469',
		'f947d0e1-1dff-4951-bab1-4a49e16333d5',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Foundation piling and underpinning',
		NULL,
		'Standard'
	),
	(
		'd904e4f9-e6f2-4293-949a-f1888ee1f678',
		'f947d0e1-1dff-4951-bab1-4a49e16333d5',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Foundations up to top of lowest floor slabs',
		NULL,
		'Standard'
	),
	(
		'17e72ce7-b00a-429f-847a-231017154297',
		'f947d0e1-1dff-4951-bab1-4a49e16333d5',
		icms_v3_library_id,
		4,
		'030',
		'************',
		'Basement sides and bottom',
		NULL,
		'Standard'
	),
	(
		'cbc53d5d-8a6a-48ba-a639-1e6196b122dd',
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		icms_v3_library_id,
		3,
		'03',
		'01.03.03',
		'Structure',
		'All the load bearing work, including non-load bearing components and services and equipment forming an integral part of composite or prefabricated load bearing work, excluding those included in Substructure and Architectural works | Non-structural works.',
		'Standard'
	),
	(
		'77b6f4f3-28b4-4ad0-8fde-99beae705590',
		'cbc53d5d-8a6a-48ba-a639-1e6196b122dd',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Structural removal and alterations',
		NULL,
		'Standard'
	),
	(
		'cf8bf823-2abf-4ebd-a7cb-1a9bdb10bf21',
		'cbc53d5d-8a6a-48ba-a639-1e6196b122dd',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Basement suspended floors (up to top of ground floor slabs)',
		NULL,
		'Standard'
	),
	(
		'114eb0be-be81-45fd-9b89-0d3a21d2e956',
		'cbc53d5d-8a6a-48ba-a639-1e6196b122dd',
		icms_v3_library_id,
		4,
		'030',
		'************',
		'Frames and slabs (above top of ground floor slabs)',
		NULL,
		'Standard'
	),
	(
		'48220a1e-4385-4415-b2da-d0331123a81e',
		'cbc53d5d-8a6a-48ba-a639-1e6196b122dd',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Tanks, pools, sundries',
		NULL,
		'Standard'
	),
	(
		'fcb0d3f2-3a6e-4c9c-b358-b117fe113294',
		'cbc53d5d-8a6a-48ba-a639-1e6196b122dd',
		icms_v3_library_id,
		4,
		'050',
		'************',
		'Composite or prefabricated work',
		NULL,
		'Standard'
	),
	(
		'5cbfe4dc-1688-48b2-986b-9b40d3b52506',
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		icms_v3_library_id,
		3,
		'04',
		'01.03.04',
		'Architectural works | Non-structural works',
		'All architectural and non-load bearing work excluding services, equipment, and surface and underground drainage.',
		'Standard'
	),
	(
		'fd18bedd-b827-4f4f-8dbc-3ff21cfa7037',
		'5cbfe4dc-1688-48b2-986b-9b40d3b52506',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Non-structural removal and alterations',
		NULL,
		'Standard'
	),
	(
		'95b32dba-1ccb-4fe0-88ce-ae6e22bcf0cf',
		'5cbfe4dc-1688-48b2-986b-9b40d3b52506',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'External elevations',
		NULL,
		'Standard'
	),
	(
		'ce795e39-5b29-4027-baaf-27c34f2ef383',
		'5cbfe4dc-1688-48b2-986b-9b40d3b52506',
		icms_v3_library_id,
		4,
		'030',
		'************',
		'Roof finishes, skylights and landscaping (including waterproofing and insulation)',
		NULL,
		'Standard'
	),
	(
		'4fec8c9f-4fc9-4c34-be34-21d4b43b5205',
		'5cbfe4dc-1688-48b2-986b-9b40d3b52506',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Internal divisions',
		NULL,
		'Standard'
	),
	(
		'4cb9dfa6-8f78-440c-825d-f8c31e2a7794',
		'5cbfe4dc-1688-48b2-986b-9b40d3b52506',
		icms_v3_library_id,
		4,
		'050',
		'01.03.04.050',
		'Fittings and sundries',
		NULL,
		'Standard'
	),
	(
		'f8b2ed4d-f626-47a4-b19a-db8e6e0077e8',
		'5cbfe4dc-1688-48b2-986b-9b40d3b52506',
		icms_v3_library_id,
		4,
		'060',
		'01.03.04.060',
		'Finishes under cover',
		NULL,
		'Standard'
	),
	(
		'd88ea00e-b93b-44b3-b013-21424b5dba45',
		'5cbfe4dc-1688-48b2-986b-9b40d3b52506',
		icms_v3_library_id,
		4,
		'070',
		'************',
		'Builder’s work in connection with services',
		NULL,
		'Standard'
	),
	(
		'54e56401-c614-4c02-92d4-82529792a986',
		'5cbfe4dc-1688-48b2-986b-9b40d3b52506',
		icms_v3_library_id,
		4,
		'080',
		'************',
		'Composite or prefabricated work',
		NULL,
		'Standard'
	),
	(
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		icms_v3_library_id,
		3,
		'05',
		'01.03.05',
		'Services and equipment',
		'All fixed services and equipment required [to put the completed project into use for Construction Costs | to sustain the use after completion of construction for Renewal and Maintenance Costs], whether they are mechanical, hydraulic, plumbing, fire-fighting, transport, communication, security, electrical or electronic, control systems, or signalling excluding external surface and underground drainage. Including testing, commissioning and operational licensing and plant upgrades/refurbishment.',
		'Standard'
	),
	(
		'0ee2d888-8bfa-4463-97ae-a91fd00e039f',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Heating, ventilating and air-conditioning systems/air conditioners',
		NULL,
		'Standard'
	),
	(
		'ee9c9577-8102-427a-babe-f258e93ef275',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Electrical services',
		NULL,
		'Standard'
	),
	(
		'db7983ed-07be-4a6e-abde-79ded5b3de2d',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'030',
		'01.03.05.030',
		'Fitting out lighting fittings',
		NULL,
		'Standard'
	),
	(
		'619eb31a-00ee-49a1-92a1-8b5f99444803',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'040',
		'01.03.05.040',
		'Extra low voltage electrical services',
		NULL,
		'Standard'
	),
	(
		'7c6c7c92-4299-4f0a-af37-16ad4a051b42',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'050',
		'01.03.05.050',
		'Water supply and drainage above ground or inside basement',
		NULL,
		'Standard'
	),
	(
		'9c0deefd-91c0-4780-9099-183290fcd389',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'060',
		'01.03.05.060',
		'Supply of sanitary fittings and fixtures',
		NULL,
		'Standard'
	),
	(
		'fec83743-56b9-4220-80f4-198552b85377',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'070',
		'01.03.05.070',
		'Disposal systems',
		NULL,
		'Standard'
	),
	(
		'6e5561fb-5121-4170-9425-2e3844f2459a',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'080',
		'01.03.05.080',
		'Fire services',
		NULL,
		'Standard'
	),
	(
		'59fdf426-0bae-4afd-ad87-5569cd3fa661',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'090',
		'01.03.05.090',
		'Gas services',
		NULL,
		'Standard'
	),
	(
		'6e6138b3-2a65-46ab-96de-462b944a9ae2',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'100',
		'01.03.05.100',
		'Movement systems',
		NULL,
		'Standard'
	),
	(
		'a04b3477-1100-4c6e-9ac5-7daa2562209b',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'110',
		'01.03.05.110',
		'Gondolas',
		NULL,
		'Standard'
	),
	(
		'a482d74b-f37b-4d28-a974-23112712ad37',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'120',
		'01.03.05.120',
		'Turntables',
		NULL,
		'Standard'
	),
	(
		'7a1e82d1-51ff-4e48-bd91-f807dd9ad8ab',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'130',
		'************',
		'Generators',
		NULL,
		'Standard'
	),
	(
		'839d4e9c-26dc-4897-8fa5-b3b6a26858f4',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'140',
		'************',
		'Energy-saving features',
		NULL,
		'Standard'
	),
	(
		'2750c908-8f00-45ed-99ac-8f8facb49355',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'150',
		'************',
		'Water and wastewater treatment equipment',
		NULL,
		'Standard'
	),
	(
		'92a9c184-cd0f-4e42-9255-04f9818ce2b9',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'160',
		'************',
		'Fountains, pools and filtration plant',
		NULL,
		'Standard'
	),
	(
		'cbbfc59f-faaa-4261-8390-f92706b57ca6',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'170',
		'************',
		'Powered building signage',
		NULL,
		'Standard'
	),
	(
		'd6eb717c-976c-4f4a-b9d5-df3cec0bb2e0',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'175',
		'************',
		'Audio/visual entertainment system',
		NULL,
		'Standard'
	),
	(
		'00a7452a-ccdf-4834-b347-06aa7e718fd6',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'180',
		'************',
		'Kitchen equipment',
		NULL,
		'Standard'
	),
	(
		'7fce7c2b-21d8-467a-b97e-4cce8e721b73',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'190',
		'************',
		'Cold room equipment',
		NULL,
		'Standard'
	),
	(
		'2761e1b9-58a2-4efc-9392-2e5bcc20d4b0',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'200',
		'************',
		'Laboratory equipment',
		NULL,
		'Standard'
	),
	(
		'f7ad59f6-6636-40e9-aa1e-df2c03240370',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'210',
		'************',
		'Medical equipment',
		NULL,
		'Standard'
	),
	(
		'e8033734-e327-4acf-897b-e6568e6a2f47',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'220',
		'************',
		'Hotel equipment',
		NULL,
		'Standard'
	),
	(
		'28b75340-df3a-445f-8b7a-19a6cdc3b5ff',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'230',
		'************',
		'Car park or entrances access control',
		NULL,
		'Standard'
	),
	(
		'354a0680-31ed-47aa-ab12-13099698f105',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'240',
		'************',
		'Domestic appliances',
		NULL,
		'Standard'
	),
	(
		'07540c82-ee85-49ee-ab77-eff220440430',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'250',
		'************',
		'Other specialist services',
		NULL,
		'Standard'
	),
	(
		'4cc3730a-6bc3-48e1-a42b-466b52244437',
		'46416bd5-d885-4920-8393-c88e92a1b77c',
		icms_v3_library_id,
		4,
		'260',
		'01.03.05.260',
		'Builder’s profit and attendance on services',
		NULL,
		'Standard'
	),
	(
		'4b925a0f-30e4-4d52-a211-f6bf78380543',
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		icms_v3_library_id,
		3,
		'06',
		'01.03.06',
		'Surface and underground drainage',
		'All underground or external surface drainage systems excluding those inside basement or underground construction.',
		'Standard'
	),
	(
		'eb9096e3-dc75-4867-8eba-70fed305fffd',
		'4b925a0f-30e4-4d52-a211-f6bf78380543',
		icms_v3_library_id,
		4,
		'010',
		'01.03.06.010',
		'Surface water drainage',
		NULL,
		'Standard'
	),
	(
		'dd449779-5403-4cd9-a2d9-19eb543d1a2f',
		'4b925a0f-30e4-4d52-a211-f6bf78380543',
		icms_v3_library_id,
		4,
		'020',
		'01.03.06.020',
		'Storm water drainage',
		NULL,
		'Standard'
	),
	(
		'7c3bb0ee-af15-48ba-b7b7-9b444ec7f995',
		'4b925a0f-30e4-4d52-a211-f6bf78380543',
		icms_v3_library_id,
		4,
		'030',
		'01.03.06.030',
		'Foul and wastewater drainage',
		NULL,
		'Standard'
	),
	(
		'63e28427-18ff-45ff-997f-250022eff1c4',
		'4b925a0f-30e4-4d52-a211-f6bf78380543',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Drainage disconnections and connections',
		NULL,
		'Standard'
	),
	(
		'7fd7ed13-c2a2-4555-b95e-d4b243c4559e',
		'4b925a0f-30e4-4d52-a211-f6bf78380543',
		icms_v3_library_id,
		4,
		'050',
		'************',
		'CCTV inspection of existing or new drains',
		NULL,
		'Standard'
	),
	(
		'ea0eb4a0-d90f-4028-92da-65fcd46cbf1f',
		'4b925a0f-30e4-4d52-a211-f6bf78380543',
		icms_v3_library_id,
		4,
		'060',
		'************',
		'Buried Process Pipe',
		NULL,
		'Standard'
	),
	(
		'362e3af2-503d-42b7-b5f0-a3f8e9a14377',
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		icms_v3_library_id,
		3,
		'07',
		'01.03.07',
		'External and ancillary works',
		'All work outside the external face of buildings or beyond the construction entity required to fulfil the primary function of the Project and not included in other Groups.',
		'Standard'
	),
	(
		'3845d0dc-40fa-47ba-bb86-13066e947f4d',
		'362e3af2-503d-42b7-b5f0-a3f8e9a14377',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Permanent retaining structures',
		NULL,
		'Standard'
	),
	(
		'09b730d8-da28-4e16-ad4c-4f09413cfe05',
		'362e3af2-503d-42b7-b5f0-a3f8e9a14377',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Site enclosures and divisions',
		NULL,
		'Standard'
	),
	(
		'9b1facf6-e523-4e5e-9327-87fd3f6b7816',
		'362e3af2-503d-42b7-b5f0-a3f8e9a14377',
		icms_v3_library_id,
		4,
		'030',
		'************',
		'Ancillary structures',
		NULL,
		'Standard'
	),
	(
		'1606d960-d947-4a3f-bc60-47412570f2b9',
		'362e3af2-503d-42b7-b5f0-a3f8e9a14377',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Roads and paving',
		NULL,
		'Standard'
	),
	(
		'b7f9974c-52d7-4a54-99b1-45bdd9f3f42d',
		'362e3af2-503d-42b7-b5f0-a3f8e9a14377',
		icms_v3_library_id,
		4,
		'050',
		'************',
		'Landscaping (hard and soft)',
		NULL,
		'Standard'
	),
	(
		'ee1b500a-26d6-4a74-bf70-0f9f4592dda3',
		'362e3af2-503d-42b7-b5f0-a3f8e9a14377',
		icms_v3_library_id,
		4,
		'060',
		'************',
		'Fittings and equipment',
		NULL,
		'Standard'
	),
	(
		'97ba9933-1710-4ae7-8010-1e93b6772758',
		'362e3af2-503d-42b7-b5f0-a3f8e9a14377',
		icms_v3_library_id,
		4,
		'070',
		'************',
		'External services',
		NULL,
		'Standard'
	),
	(
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		icms_v3_library_id,
		3,
		'08',
		'01.03.08',
		'Preliminaries | Constructors’ site overheads | general requirements',
		'Constructors’ site management, temporary site facilities, site services, mobilisation, demobilisation and other expenses, not directly related to a particular Group, but commonly required to be shared by all Groups.',
		'Standard'
	),
	(
		'd47dc0f9-6ae5-44b2-b9a6-642c6809a8c2',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Construction management including site management staff and support labour',
		NULL,
		'Standard'
	),
	(
		'2af4e87b-c992-45b3-8f7f-aca95e0234bd',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'020',
		'01.03.08.020',
		'Temporary access roads and storage areas, traffic management and diversion (at the Constructors’ discretion)',
		NULL,
		'Standard'
	),
	(
		'3f3c7d5d-6d72-4c0b-8d0a-1eed761ba2c2',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'030',
		'01.03.08.030',
		'Temporary site fencing and securities',
		NULL,
		'Standard'
	),
	(
		'ac08b3f7-88d9-47c4-bf58-fda930928fd1',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'040',
		'01.03.08.040',
		'Commonly shared construction plant',
		NULL,
		'Standard'
	),
	(
		'c9c2fb96-972d-44ea-b019-b3bd8dbd9b5d',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'050',
		'01.03.08.050',
		'Commonly shared scaffolding',
		NULL,
		'Standard'
	),
	(
		'138c133c-f6f9-412c-b3cd-c530e72fbe71',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'060',
		'01.03.08.060',
		'Other temporary facilities and services',
		NULL,
		'Standard'
	),
	(
		'd9e5fbae-4e60-4676-b572-9fb4fc42fee2',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'070',
		'01.03.08.070',
		'Technology and communications: telephone, broadband, hardware, software',
		NULL,
		'Standard'
	),
	(
		'dba94e8c-c05b-42b5-a1a1-38a0740390e8',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'080',
		'01.03.08.080',
		'Constructor’s submissions, reports and as-built documentation',
		NULL,
		'Standard'
	),
	(
		'4c9fba00-1267-4e86-9409-1102237f9f5c',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'090',
		'01.03.08.090',
		'Quality monitoring, recording and inspections',
		NULL,
		'Standard'
	),
	(
		'bcfbcf8e-da5f-43d4-9c0c-4d7b9d04d399',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'100',
		'01.03.08.100',
		'Safety, health and environmental management',
		NULL,
		'Standard'
	),
	(
		'4e132233-def0-4ba6-bf26-688982ba8b6e',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'110',
		'01.03.08.110',
		'Insurances, bonds, guarantees and warranties',
		NULL,
		'Standard'
	),
	(
		'ff6b4daa-a10c-4aea-b5c4-5dccd8e46192',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'120',
		'01.03.08.120',
		'Constructor’s statutory fees and charges',
		NULL,
		'Standard'
	),
	(
		'f889aa37-267f-428e-b73f-c3b5499f3123',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'130',
		'01.03.08.130',
		'Testing and commissioning',
		NULL,
		'Standard'
	),
	(
		'da92b78f-79da-4d70-b887-0698987d7398',
		'146106f9-1ba2-4aa6-b23d-64dd4b3512f3',
		icms_v3_library_id,
		4,
		'140',
		'01.03.08.140',
		'Extras for extreme climatic or working conditions (if priced separately according to local pricing practice)',
		NULL,
		'Standard'
	),
	(
		'd9b15235-872f-44fb-8f29-05d11fb8e9b9',
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		icms_v3_library_id,
		3,
		'09',
		'01.03.09',
		'Risk Allowances',
		'As defined in section 4.1 but related to [Construction | Renewal | Maintenance] Costs and not included in other Groups.',
		'Standard'
	),
	(
		'6030cd9d-8868-49ee-be77-b2be6b6bc166',
		'd9b15235-872f-44fb-8f29-05d11fb8e9b9',
		icms_v3_library_id,
		4,
		'010',
		'01.03.09.010',
		'Design development allowance',
		NULL,
		'Standard'
	),
	(
		'afc42688-6e4a-448b-bdce-dd4510d45058',
		'd9b15235-872f-44fb-8f29-05d11fb8e9b9',
		icms_v3_library_id,
		4,
		'020',
		'01.03.09.020',
		'Construction contingencies',
		NULL,
		'Standard'
	),
	(
		'd00bd6ed-e0ba-4712-aaa4-b6fe1009541d',
		'd9b15235-872f-44fb-8f29-05d11fb8e9b9',
		icms_v3_library_id,
		4,
		'030',
		'01.03.09.030',
		'Price Level Adjustments',
		NULL,
		'Standard'
	),
	(
		'2f58908d-b09c-4290-afd3-2d1eed3614ca',
		'd9b15235-872f-44fb-8f29-05d11fb8e9b9',
		icms_v3_library_id,
		4,
		'040',
		'01.03.09.040',
		'Exchange rate fluctuation adjustments',
		NULL,
		'Standard'
	),
	(
		'209ba045-b2c4-483e-90ca-ea889b2cf2bb',
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		icms_v3_library_id,
		3,
		'10',
		'01.03.10',
		'Taxes and Levies',
		'As defined in section 4.1 and not included in other Groups.',
		'Standard'
	),
	(
		'6ed6735f-4d64-4db1-a9e6-8c7ed7be7656',
		'209ba045-b2c4-483e-90ca-ea889b2cf2bb',
		icms_v3_library_id,
		4,
		'010',
		'01.03.10.010',
		'Paid by the Constructor',
		NULL,
		'Standard'
	),
	(
		'ea0818ec-b766-4f78-9fca-b719cf40af2f',
		'209ba045-b2c4-483e-90ca-ea889b2cf2bb',
		icms_v3_library_id,
		4,
		'020',
		'01.03.10.020',
		'Paid by the Client in relation to the construction contract payments',
		NULL,
		'Standard'
	),
	(
		'75e9f8bb-7054-48f5-9971-f32c41587b79',
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		icms_v3_library_id,
		3,
		'11',
		'01.03.11',
		'Work and utilities off-site',
		'All payments to government authorities or public utility companies to connect | keep connected public work and utilities to the site, or services diversions, to enable the Project, including related risk allowances, taxes and levies.',
		'Standard'
	),
	(
		'e4305d9d-f5a5-4fba-b538-9a69984680f5',
		'75e9f8bb-7054-48f5-9971-f32c41587b79',
		icms_v3_library_id,
		4,
		'010',
		'01.03.11.010',
		'Connections to, diversion of and capacity enhancement of public utility mains or sources off-site up to mains connections on-site',
		NULL,
		'Standard'
	),
	(
		'8944ea60-6c41-4d02-a0c1-563394e27af8',
		'75e9f8bb-7054-48f5-9971-f32c41587b79',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Public access roads and footpaths',
		NULL,
		'Standard'
	),
	(
		'161f4ac8-24c0-4ca5-b1db-4eddc368c646',
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		icms_v3_library_id,
		3,
		'12',
		'01.03.12',
		'Production and loose furniture',
		' fittings and equipment',
		'Standard'
	),
	(
		'e265ddf3-6f89-47f7-9f43-35a2e2783616',
		'161f4ac8-24c0-4ca5-b1db-4eddc368c646',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Loose production, process and operating furniture, fittings and equipment not normally provided before completion of construction',
		NULL,
		'Standard'
	),
	(
		'e52ef9bd-98e7-4e03-a8e5-491ab964f08f',
		'161f4ac8-24c0-4ca5-b1db-4eddc368c646',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Fixed production, process and operating furniture, fittings and equipment installed before completion of construction',
		NULL,
		'Standard'
	),
	(
		'608d525d-b924-4510-8c8b-4e48b77ffb75',
		'a26685e2-af10-4c06-84b6-503a760ae1e1',
		icms_v3_library_id,
		3,
		'13',
		'01.03.13',
		'Renewal-related consultancies and supervision',
		'Fees and charges payable to Service Providers not engaged by the Constructors, including related risk allowances, taxes and levies.',
		'Standard'
	),
	(
		'4cce1cd5-b8f7-4094-9199-5af113fee28f',
		'608d525d-b924-4510-8c8b-4e48b77ffb75',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Consultants’ fees and reimbursable',
		NULL,
		'Standard'
	),
	(
		'bc402e46-aff9-4e3a-a7cd-026aa6389df8',
		'608d525d-b924-4510-8c8b-4e48b77ffb75',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Charges and levies payable to statutory bodies or their appointed agencies',
		NULL,
		'Standard'
	),
	(
		'11773c86-cd71-4293-9fef-8dcd06504c81',
		'608d525d-b924-4510-8c8b-4e48b77ffb75',
		icms_v3_library_id,
		4,
		'030',
		'01.03.13.030',
		'Site supervision charges (including their accommodation and travels)',
		NULL,
		'Standard'
	),
	(
		'f41d4fce-bd6e-4206-bd47-cc68179b1fac',
		'608d525d-b924-4510-8c8b-4e48b77ffb75',
		icms_v3_library_id,
		4,
		'040',
		'01.03.13.040',
		'Payments to testing authorities or laboratories',
		NULL,
		'Standard'
	),
	(
		'99025114-21fb-4b74-a3d5-daccb04866ef',
		'c777ec64-2466-4d63-a315-2fb7a116403f',
		icms_v3_library_id,
		2,
		'04',
		'01.04',
		'Operation Costs (OC)',
		'Costs incurred in running and managing a Constructed Asset during occupation , including administrative support services, rent, insurances, energy and other environmental/regulatory inspection costs, taxes and charges.',
		'Standard'
	),
	(
		'544738df-7177-4ca0-af50-7b1471bd92eb',
		'99025114-21fb-4b74-a3d5-daccb04866ef',
		icms_v3_library_id,
		3,
		'01',
		'01.04.01',
		'Cleaning',
		'Periodic, routine and specialist cleaning of internal and external works.',
		'Standard'
	),
	(
		'4d9c4d63-57a0-4130-98df-115ab476f702',
		'544738df-7177-4ca0-af50-7b1471bd92eb',
		icms_v3_library_id,
		4,
		'010',
		'01.04.01.010',
		'External cleaning (routine and periodic)',
		NULL,
		'Standard'
	),
	(
		'4d04dba0-8fa8-4bce-8020-ea10f9c07ed7',
		'544738df-7177-4ca0-af50-7b1471bd92eb',
		icms_v3_library_id,
		4,
		'020',
		'01.04.01.020',
		'Internal cleaning (routine and periodic)',
		NULL,
		'Standard'
	),
	(
		'416dad78-40fa-4c21-91b1-d7f8ffaa7087',
		'544738df-7177-4ca0-af50-7b1471bd92eb',
		icms_v3_library_id,
		4,
		'030',
		'01.04.01.030',
		'Specialist cleaning (define type)',
		NULL,
		'Standard'
	),
	(
		'868b7d9c-2506-4434-a08f-6a17a4c61ef5',
		'99025114-21fb-4b74-a3d5-daccb04866ef',
		icms_v3_library_id,
		3,
		'02',
		'01.04.02',
		'Utilities',
		'Fuel, including gas, electricity, fuel oil solid and other fuel; water and drainage including water rates, effluents sewerage drainage and other charges.',
		'Standard'
	),
	(
		'ab714e9f-8d70-4409-8320-2ae06d0e318e',
		'868b7d9c-2506-4434-a08f-6a17a4c61ef5',
		icms_v3_library_id,
		4,
		'010',
		'01.04.02.010',
		'Fuel (state type: gas / electricity / oil and other fuel sources)',
		NULL,
		'Standard'
	),
	(
		'6983dc29-1e73-4360-830c-145bf50e00ea',
		'868b7d9c-2506-4434-a08f-6a17a4c61ef5',
		icms_v3_library_id,
		4,
		'020',
		'01.04.02.020',
		'Water, drainage and sewerage',
		NULL,
		'Standard'
	),
	(
		'2c48b508-3292-455a-b58d-dbda48ad8627',
		'99025114-21fb-4b74-a3d5-daccb04866ef',
		icms_v3_library_id,
		3,
		'03',
		'01.04.03',
		'Waste management',
		'Collection, compaction, removal and disposal and/or recycling general and toxic waste from the constructed asset.',
		'Standard'
	),
	(
		'b073f462-fe01-4c74-a505-a046491d9844',
		'2c48b508-3292-455a-b58d-dbda48ad8627',
		icms_v3_library_id,
		4,
		'010',
		'01.04.03.010',
		'Waste collection and disposal',
		NULL,
		'Standard'
	),
	(
		'5756c5fc-43f3-4eb6-862d-ec8fb0d245f6',
		'2c48b508-3292-455a-b58d-dbda48ad8627',
		icms_v3_library_id,
		4,
		'020',
		'01.04.03.020',
		'Recycling and salvage',
		NULL,
		'Standard'
	),
	(
		'dd81d4e6-5887-43e3-bb72-747bcc30f6f8',
		'99025114-21fb-4b74-a3d5-daccb04866ef',
		icms_v3_library_id,
		3,
		'04',
		'01.04.04',
		'Security',
		'Physical security (such as access control, CCTV camera) including staff or contractors involved in providing security controls via remote support centres, to the constructed asset.',
		'Standard'
	),
	(
		'1001d4a1-5db1-4de9-8b8f-8ef1d30d8a4b',
		'dd81d4e6-5887-43e3-bb72-747bcc30f6f8',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Physical security',
		NULL,
		'Standard'
	),
	(
		'f9be08a3-95d8-425b-9a74-0ec94b3a3e11',
		'dd81d4e6-5887-43e3-bb72-747bcc30f6f8',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Remote monitoring',
		NULL,
		'Standard'
	),
	(
		'72a0a554-3d38-4fbb-a720-63b0e1a44cd9',
		'99025114-21fb-4b74-a3d5-daccb04866ef',
		icms_v3_library_id,
		3,
		'05',
		'01.04.05',
		'Information and communications technology',
		'Information communications systems (such as public address and communications cabling and IT support services built as a constructed asset, as well as technology used for monitoring assets (i.e. building management systems) and physical sensors.',
		'Standard'
	),
	(
		'91d8bcf8-782e-4e4b-b1aa-50029820d936',
		'72a0a554-3d38-4fbb-a720-63b0e1a44cd9',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Communication systems',
		NULL,
		'Standard'
	),
	(
		'988d5eca-58f8-4950-be30-05d7d7fdbf0f',
		'72a0a554-3d38-4fbb-a720-63b0e1a44cd9',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Specialist technology / sensors',
		NULL,
		'Standard'
	),
	(
		'54ea1647-b4ac-4993-8853-c3c7721b01f1',
		'99025114-21fb-4b74-a3d5-daccb04866ef',
		icms_v3_library_id,
		3,
		'06',
		'01.04.06',
		'Operators’ site overheads | general requirements',
		'Operators’ site management, temporary site facilities, site services, and expenses, not directly related to a particular Group, but commonly required to be shared by all Groups.',
		'Standard'
	),
	(
		'3cc69747-3500-483d-8bf9-7dad2812bc1c',
		'54ea1647-b4ac-4993-8853-c3c7721b01f1',
		icms_v3_library_id,
		4,
		'010',
		'01.04.06.010',
		'Administration',
		NULL,
		'Standard'
	),
	(
		'7b154914-54d0-4c51-9b6c-38cc1f83614a',
		'54ea1647-b4ac-4993-8853-c3c7721b01f1',
		icms_v3_library_id,
		4,
		'020',
		'01.04.06.020',
		'Property insurance',
		NULL,
		'Standard'
	),
	(
		'3b1c491b-0f6f-47ee-ae04-dbfa2c5cad79',
		'99025114-21fb-4b74-a3d5-daccb04866ef',
		icms_v3_library_id,
		3,
		'07',
		'01.04.07',
		'Risk Allowances',
		'As defined in Part 4.1 but related to Operation Costs and not included in other Groups.',
		'Standard'
	),
	(
		'30208e86-9dfc-430a-b9f6-fdf80e38ba58',
		'3b1c491b-0f6f-47ee-ae04-dbfa2c5cad79',
		icms_v3_library_id,
		4,
		'010',
		'01.04.07.010',
		'Operation related (user definable)',
		NULL,
		'Standard'
	),
	(
		'5ef63f04-0fff-44d4-9eff-59a16b5285a0',
		'3b1c491b-0f6f-47ee-ae04-dbfa2c5cad79',
		icms_v3_library_id,
		4,
		'020',
		'01.04.07.020',
		'Contractual obligations',
		NULL,
		'Standard'
	),
	(
		'36510ab0-6292-4495-9634-63185b11863a',
		'99025114-21fb-4b74-a3d5-daccb04866ef',
		icms_v3_library_id,
		3,
		'08',
		'01.04.08',
		'Taxes and Levies',
		'As defined in Part 4.1 but related to Operation Costs.',
		'Standard'
	),
	(
		'655d2311-ee3d-4ede-bb50-028160a3d5f1',
		'36510ab0-6292-4495-9634-63185b11863a',
		icms_v3_library_id,
		4,
		'010',
		'01.04.08.010',
		'Taxes',
		NULL,
		'Standard'
	),
	(
		'd9c4d983-a51d-4012-9507-2516f3778b29',
		'36510ab0-6292-4495-9634-63185b11863a',
		icms_v3_library_id,
		4,
		'020',
		'01.04.08.020',
		'Levies',
		NULL,
		'Standard'
	),
	(
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		'c777ec64-2466-4d63-a315-2fb7a116403f',
		icms_v3_library_id,
		2,
		'05',
		'01.05',
		'Maintenance Costs (MC)',
		'The total cost of labour, material and other related costs to retain a Constructed Asset or its parts so that it can perform its required functions. Maintenance includes conducting corrective, responsive and preventative maintenance on a Constructed Asset or its parts and all associated management, cleaning, services, repainting, repairing or replacing of parts as needed for the Constructed Asset to be used for its intended purpose. It does not include Renewal Costs.',
		'Standard'
	),
	(
		'98ba5c6e-dfa0-4f99-9c18-f504818e39e8',
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		icms_v3_library_id,
		3,
		'01',
		'01.05.01',
		'Demolition',
		' site preparation and formation',
		'Standard'
	),
	(
		'780e1df9-e847-4897-af6f-12930e53d1ae',
		'98ba5c6e-dfa0-4f99-9c18-f504818e39e8',
		icms_v3_library_id,
		4,
		'010',
		'01.05.01.010',
		'Site survey and ground investigation',
		NULL,
		'Standard'
	),
	(
		'1fc68e38-2ff3-45cb-839f-3ca887fe4f92',
		'98ba5c6e-dfa0-4f99-9c18-f504818e39e8',
		icms_v3_library_id,
		4,
		'020',
		'01.05.01.020',
		'Environmental treatment',
		NULL,
		'Standard'
	),
	(
		'384eb21f-4204-4adc-ae2a-9e64837b6c2c',
		'98ba5c6e-dfa0-4f99-9c18-f504818e39e8',
		icms_v3_library_id,
		4,
		'030',
		'01.05.01.030',
		'Sampling of hazardous or useful materials or conditions',
		NULL,
		'Standard'
	),
	(
		'0676f168-b316-4c8c-aafc-8a22039a4aa7',
		'98ba5c6e-dfa0-4f99-9c18-f504818e39e8',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Temporary fencing',
		NULL,
		'Standard'
	),
	(
		'a5c7182d-f5d0-44f2-a192-6bc13726f6b8',
		'98ba5c6e-dfa0-4f99-9c18-f504818e39e8',
		icms_v3_library_id,
		4,
		'050',
		'************',
		'Demolition of existing buildings and support to adjacent structures',
		NULL,
		'Standard'
	),
	(
		'6ed55685-b72b-4dd1-99db-c6385ace5a5b',
		'98ba5c6e-dfa0-4f99-9c18-f504818e39e8',
		icms_v3_library_id,
		4,
		'060',
		'************',
		'Site surface clearance (clearing, grubbing, topsoil stripping, tree felling, minor earthwork, removal)',
		NULL,
		'Standard'
	),
	(
		'901c7823-d460-46f6-9f1d-ba6a2d458ce5',
		'98ba5c6e-dfa0-4f99-9c18-f504818e39e8',
		icms_v3_library_id,
		4,
		'070',
		'************',
		'Tree transplant',
		NULL,
		'Standard'
	),
	(
		'dc2811ad-fb66-4caa-ac8c-77438571be64',
		'98ba5c6e-dfa0-4f99-9c18-f504818e39e8',
		icms_v3_library_id,
		4,
		'080',
		'************',
		'Site formation and slope treatment',
		NULL,
		'Standard'
	),
	(
		'83a83f50-c2dd-4831-bde4-1d5ad4aa9114',
		'98ba5c6e-dfa0-4f99-9c18-f504818e39e8',
		icms_v3_library_id,
		4,
		'090',
		'************',
		'Temporary surface drainage and dewatering',
		NULL,
		'Standard'
	),
	(
		'8ce0d05c-cb99-4528-a314-af80ee06f6bc',
		'98ba5c6e-dfa0-4f99-9c18-f504818e39e8',
		icms_v3_library_id,
		4,
		'100',
		'************',
		'Temporary protection, diversion and relocation of public utilities',
		NULL,
		'Standard'
	),
	(
		'd3e8abff-b517-4dc9-b9bc-a0062bc4f6c7',
		'98ba5c6e-dfa0-4f99-9c18-f504818e39e8',
		icms_v3_library_id,
		4,
		'110',
		'************',
		'Erosion control',
		NULL,
		'Standard'
	),
	(
		'967b73e1-f517-489f-b778-0122179d681d',
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		icms_v3_library_id,
		3,
		'02',
		'01.05.02',
		'Substructure',
		'All the load bearing work underground or underwater up to and including the following (including related earthwork, lateral support beyond site formation, and non-load bearing components and services and equipment forming an integral part of composite or prefabricated load bearing work) and as illustrated in Part 4.2: for buildings: lowest floor slabs, and basement sides and bottom including related waterproofing and insulation',
		'Standard'
	),
	(
		'574b52b9-9080-4e35-bf18-de74a7cdc1a3',
		'967b73e1-f517-489f-b778-0122179d681d',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Foundation piling and underpinning',
		NULL,
		'Standard'
	),
	(
		'351b87a3-3637-4241-8634-26addfdc3ab8',
		'967b73e1-f517-489f-b778-0122179d681d',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Foundations up to top of lowest floor slabs',
		NULL,
		'Standard'
	),
	(
		'9fee4342-fa45-41a8-8e0b-3526de17c379',
		'967b73e1-f517-489f-b778-0122179d681d',
		icms_v3_library_id,
		4,
		'030',
		'************',
		'Basement sides and bottom',
		NULL,
		'Standard'
	),
	(
		'26bdf24b-ff07-46e9-98b9-d889057da1a6',
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		icms_v3_library_id,
		3,
		'03',
		'01.05.03',
		'Structure',
		'All the load bearing work, including non-load bearing components and services and equipment forming an integral part of composite or prefabricated load bearing work, excluding those included in Substructure and Architectural works | Non-structural works.',
		'Standard'
	),
	(
		'b923373c-c54f-4b57-9609-dde9d33e8c5e',
		'26bdf24b-ff07-46e9-98b9-d889057da1a6',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Structural removal and alterations',
		NULL,
		'Standard'
	),
	(
		'06556b5b-cb5d-4302-a464-2639005dc0ba',
		'26bdf24b-ff07-46e9-98b9-d889057da1a6',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Basement suspended floors (up to top of ground floor slabs)',
		NULL,
		'Standard'
	),
	(
		'b67518a4-e2ca-49f2-bcb6-63fe3e9a8835',
		'26bdf24b-ff07-46e9-98b9-d889057da1a6',
		icms_v3_library_id,
		4,
		'030',
		'************',
		'Frames and slabs (above top of ground floor slabs)',
		NULL,
		'Standard'
	),
	(
		'9d0598cf-4840-452c-820c-f0526592c3ff',
		'26bdf24b-ff07-46e9-98b9-d889057da1a6',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Tanks, pools, sundries',
		NULL,
		'Standard'
	),
	(
		'de3cba21-7a63-477e-bf3c-a754a867a0a5',
		'26bdf24b-ff07-46e9-98b9-d889057da1a6',
		icms_v3_library_id,
		4,
		'050',
		'************',
		'Composite or prefabricated work',
		NULL,
		'Standard'
	),
	(
		'69ed9f7f-2d14-40dc-b81a-02eb7fdb7b93',
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		icms_v3_library_id,
		3,
		'04',
		'01.05.04',
		'Architectural works | Non-structural works',
		'All architectural and non-load bearing work excluding services, equipment, and surface and underground drainage.',
		'Standard'
	),
	(
		'0ec00308-c36b-4f5c-a12b-e2280339d3ac',
		'69ed9f7f-2d14-40dc-b81a-02eb7fdb7b93',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Non-structural removal and alterations',
		NULL,
		'Standard'
	),
	(
		'67046a50-3d43-4ba2-b451-07149f7ddfb9',
		'69ed9f7f-2d14-40dc-b81a-02eb7fdb7b93',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'External elevations',
		NULL,
		'Standard'
	),
	(
		'6bc70166-7b50-459b-8f4e-08d40f7018f5',
		'69ed9f7f-2d14-40dc-b81a-02eb7fdb7b93',
		icms_v3_library_id,
		4,
		'030',
		'************',
		'Roof finishes, skylights and landscaping (including waterproofing and insulation)',
		NULL,
		'Standard'
	),
	(
		'71318c06-31bb-4274-afff-244f09d272b0',
		'69ed9f7f-2d14-40dc-b81a-02eb7fdb7b93',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Internal divisions',
		NULL,
		'Standard'
	),
	(
		'fb8d47aa-fd28-46c8-af43-e1f9c0e57f06',
		'69ed9f7f-2d14-40dc-b81a-02eb7fdb7b93',
		icms_v3_library_id,
		4,
		'050',
		'01.05.04.050',
		'Fittings and sundries',
		NULL,
		'Standard'
	),
	(
		'9eaa36c8-196c-44bc-a711-8ac1cdf83d1e',
		'69ed9f7f-2d14-40dc-b81a-02eb7fdb7b93',
		icms_v3_library_id,
		4,
		'060',
		'01.05.04.060',
		'Finishes under cover',
		NULL,
		'Standard'
	),
	(
		'960a6a29-4c2f-4eb0-87ae-5d9d2db634f4',
		'69ed9f7f-2d14-40dc-b81a-02eb7fdb7b93',
		icms_v3_library_id,
		4,
		'070',
		'************',
		'Builder’s work in connection with services',
		NULL,
		'Standard'
	),
	(
		'b92dd169-ff6b-41dc-91cd-31b2e58fd0dc',
		'69ed9f7f-2d14-40dc-b81a-02eb7fdb7b93',
		icms_v3_library_id,
		4,
		'080',
		'************',
		'Composite or prefabricated work',
		NULL,
		'Standard'
	),
	(
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		icms_v3_library_id,
		3,
		'05',
		'01.05.05',
		'Services and equipment',
		'All fixed services and equipment required [to put the completed project into use for Construction Costs | to sustain the use after completion of construction for Renewal and Maintenance Costs], whether they are mechanical, hydraulic, plumbing, fire-fighting, transport, communication, security, electrical or electronic, control systems, or signalling excluding external surface and underground drainage. Including testing, commissioning and operational licensing and plant upgrades/refurbishment.',
		'Standard'
	),
	(
		'a69c6558-34a0-42dd-9a6f-04e781e5ba59',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Heating, ventilating and air-conditioning systems/air conditioners',
		NULL,
		'Standard'
	),
	(
		'a305febb-0eb6-4d27-ae53-7f3cecff814f',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Electrical services',
		NULL,
		'Standard'
	),
	(
		'0ecf428a-cb45-4ad0-b0c6-c9be82f61f0c',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'030',
		'01.05.05.030',
		'Fitting out lighting fittings',
		NULL,
		'Standard'
	),
	(
		'e3ea529d-4d27-434b-985c-a4f3157a333c',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'040',
		'01.05.05.040',
		'Extra low voltage electrical services',
		NULL,
		'Standard'
	),
	(
		'45cedd1d-8ef6-434c-bf36-08e3f80f78ce',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'050',
		'01.05.05.050',
		'Water supply and drainage above ground or inside basement',
		NULL,
		'Standard'
	),
	(
		'541d4ff7-dfdd-46f5-924d-7604f4cd0af3',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'060',
		'01.05.05.060',
		'Supply of sanitary fittings and fixtures',
		NULL,
		'Standard'
	),
	(
		'a6b32014-0ed5-42fc-88c7-a0c15f8dba91',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'070',
		'01.05.05.070',
		'Disposal systems',
		NULL,
		'Standard'
	),
	(
		'6cc6907b-ad08-4a07-b9ab-01292d8dece8',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'080',
		'01.05.05.080',
		'Fire services',
		NULL,
		'Standard'
	),
	(
		'b7fe9160-32cb-4e44-b444-c782a11a06ba',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'090',
		'01.05.05.090',
		'Gas services',
		NULL,
		'Standard'
	),
	(
		'e9877af7-b608-4ebc-9b8f-098748ba3768',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'100',
		'01.05.05.100',
		'Movement systems',
		NULL,
		'Standard'
	),
	(
		'bf2523d7-eb8e-42f5-8c5e-6677b83aee9a',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'110',
		'01.05.05.110',
		'Gondolas',
		NULL,
		'Standard'
	),
	(
		'0e70954d-e945-45b3-99a6-a7c71afcc0c7',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'120',
		'01.05.05.120',
		'Turntables',
		NULL,
		'Standard'
	),
	(
		'ebb61125-6913-4cf5-b851-3266ff2e358d',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'130',
		'************',
		'Generators',
		NULL,
		'Standard'
	),
	(
		'b898bef4-9599-4f1c-ae73-4dfeb3c09bc5',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'140',
		'************',
		'Energy-saving features',
		NULL,
		'Standard'
	),
	(
		'966917bb-91b8-4c36-8062-63920da6b106',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'150',
		'************',
		'Water and wastewater treatment equipment',
		NULL,
		'Standard'
	),
	(
		'216b706c-0982-4d07-9361-d6d64194469d',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'160',
		'************',
		'Fountains, pools and filtration plant',
		NULL,
		'Standard'
	),
	(
		'b07a70e8-9e9c-4ef0-9080-a6f2e3f109b7',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'170',
		'************',
		'Powered building signage',
		NULL,
		'Standard'
	),
	(
		'a021c5c8-61e3-4359-a117-d986aeb22738',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'175',
		'************',
		'Audio/visual entertainment system',
		NULL,
		'Standard'
	),
	(
		'cc48da91-abfe-4782-91df-cd3f0cdfc613',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'180',
		'************',
		'Kitchen equipment',
		NULL,
		'Standard'
	),
	(
		'eaed15dd-02b3-4b0e-bf5d-96a3643c6794',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'190',
		'************',
		'Cold room equipment',
		NULL,
		'Standard'
	),
	(
		'ba79cd66-5bbe-4a37-82d6-13616b074360',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'200',
		'************',
		'Laboratory equipment',
		NULL,
		'Standard'
	),
	(
		'a9dfea33-15ad-4f5a-8a35-e41ef7247e9c',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'210',
		'************',
		'Medical equipment',
		NULL,
		'Standard'
	),
	(
		'e47d28d8-6d6e-45f8-b705-f6a57ada36e5',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'220',
		'************',
		'Hotel equipment',
		NULL,
		'Standard'
	),
	(
		'539ff2cf-bb11-4912-9071-1042d6b21ca2',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'230',
		'************',
		'Car park or entrances access control',
		NULL,
		'Standard'
	),
	(
		'273fb7a9-bb70-4cf8-8fcf-7d9314526c3f',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'240',
		'************',
		'Domestic appliances',
		NULL,
		'Standard'
	),
	(
		'bd86baf3-46b0-4b65-bd60-46e86ad749a1',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'250',
		'************',
		'Other specialist services',
		NULL,
		'Standard'
	),
	(
		'2864aa0d-71ab-454d-bbd8-e58a681b6d10',
		'1761474f-d568-4da7-b7cb-14047f7b18e8',
		icms_v3_library_id,
		4,
		'260',
		'01.05.05.260',
		'Builder’s profit and attendance on services',
		NULL,
		'Standard'
	),
	(
		'a8cfe572-71e7-4fb2-afa1-a10a7d357441',
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		icms_v3_library_id,
		3,
		'06',
		'01.05.06',
		'Surface and underground drainage',
		'All underground or external surface drainage systems excluding those inside basement or underground construction.',
		'Standard'
	),
	(
		'3d3fc8b7-04b8-4cce-95f5-3abe9c500080',
		'a8cfe572-71e7-4fb2-afa1-a10a7d357441',
		icms_v3_library_id,
		4,
		'010',
		'01.05.06.010',
		'Surface water drainage',
		NULL,
		'Standard'
	),
	(
		'353bcc20-1b33-49ec-99a9-0beb43c92591',
		'a8cfe572-71e7-4fb2-afa1-a10a7d357441',
		icms_v3_library_id,
		4,
		'020',
		'01.05.06.020',
		'Storm water drainage',
		NULL,
		'Standard'
	),
	(
		'0640c027-22e3-4144-82eb-3292cf1e3105',
		'a8cfe572-71e7-4fb2-afa1-a10a7d357441',
		icms_v3_library_id,
		4,
		'030',
		'01.05.06.030',
		'Foul and wastewater drainage',
		NULL,
		'Standard'
	),
	(
		'f3f41efe-78d8-447c-a156-ae227c6a09f3',
		'a8cfe572-71e7-4fb2-afa1-a10a7d357441',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Drainage disconnections and connections',
		NULL,
		'Standard'
	),
	(
		'65828cc9-e910-4f1a-ac6c-ef71253e4a0f',
		'a8cfe572-71e7-4fb2-afa1-a10a7d357441',
		icms_v3_library_id,
		4,
		'050',
		'************',
		'CCTV inspection of existing or new drains',
		NULL,
		'Standard'
	),
	(
		'debf2aad-c0c1-4e67-abfd-34a4fe592bfa',
		'a8cfe572-71e7-4fb2-afa1-a10a7d357441',
		icms_v3_library_id,
		4,
		'060',
		'************',
		'Buried Process Pipe',
		NULL,
		'Standard'
	),
	(
		'a0cf0871-5f86-42fb-967d-76132a563783',
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		icms_v3_library_id,
		3,
		'07',
		'01.05.07',
		'External and ancillary works',
		'All work outside the external face of buildings or beyond the construction entity required to fulfil the primary function of the Project and not included in other Groups.',
		'Standard'
	),
	(
		'e83d18be-6bac-4d16-8f48-9b8d9c1d2290',
		'a0cf0871-5f86-42fb-967d-76132a563783',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Permanent retaining structures',
		NULL,
		'Standard'
	),
	(
		'c8a47f7b-8c44-439d-8d3c-b43fa852821b',
		'a0cf0871-5f86-42fb-967d-76132a563783',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Site enclosures and divisions',
		NULL,
		'Standard'
	),
	(
		'd6d4ebe7-0701-42b5-b5e8-f6ca8f23f6e1',
		'a0cf0871-5f86-42fb-967d-76132a563783',
		icms_v3_library_id,
		4,
		'030',
		'************',
		'Ancillary structures',
		NULL,
		'Standard'
	),
	(
		'cfad1f44-b2c5-469a-8038-b1d03df618b5',
		'a0cf0871-5f86-42fb-967d-76132a563783',
		icms_v3_library_id,
		4,
		'040',
		'************',
		'Roads and paving',
		NULL,
		'Standard'
	),
	(
		'550f5b80-f197-41a9-9474-e227effa9f97',
		'a0cf0871-5f86-42fb-967d-76132a563783',
		icms_v3_library_id,
		4,
		'050',
		'************',
		'Landscaping (hard and soft)',
		NULL,
		'Standard'
	),
	(
		'b5ecaf68-95aa-4574-90d8-a34f72965135',
		'a0cf0871-5f86-42fb-967d-76132a563783',
		icms_v3_library_id,
		4,
		'060',
		'************',
		'Fittings and equipment',
		NULL,
		'Standard'
	),
	(
		'269f2e6e-4ea4-40cb-95cc-231b37a984b7',
		'a0cf0871-5f86-42fb-967d-76132a563783',
		icms_v3_library_id,
		4,
		'070',
		'************',
		'External services',
		NULL,
		'Standard'
	),
	(
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		icms_v3_library_id,
		3,
		'08',
		'01.05.08',
		'Preliminaries | Constructors’ site overheads | general requirements',
		'Constructors’ site management, temporary site facilities, site services, mobilisation, demobilisation and other expenses, not directly related to a particular Group, but commonly required to be shared by all Groups.',
		'Standard'
	),
	(
		'1c9c9b9f-fee4-4e8b-9618-acb9782859dc',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Construction management including site management staff and support labour',
		NULL,
		'Standard'
	),
	(
		'5fdf0d1d-ce70-4314-9e0f-c9b874e37c0c',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'020',
		'01.05.08.020',
		'Temporary access roads and storage areas, traffic management and diversion (at the Constructors’ discretion)',
		NULL,
		'Standard'
	),
	(
		'02efd646-af81-4d7a-8e59-9cc89579e649',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'030',
		'01.05.08.030',
		'Temporary site fencing and securities',
		NULL,
		'Standard'
	),
	(
		'f0c90026-7224-4945-8fc1-341ec2e38de1',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'040',
		'01.05.08.040',
		'Commonly shared construction plant',
		NULL,
		'Standard'
	),
	(
		'0648c8d1-ea5c-493e-8dfc-f4cb0a69495a',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'050',
		'01.05.08.050',
		'Commonly shared scaffolding',
		NULL,
		'Standard'
	),
	(
		'2b9ddb83-ddfb-4af6-be21-57e8139a17a8',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'060',
		'01.05.08.060',
		'Other temporary facilities and services',
		NULL,
		'Standard'
	),
	(
		'd9659aee-f3c8-4c4b-8240-286a3856c0c0',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'070',
		'01.05.08.070',
		'Technology and communications: telephone, broadband, hardware, software',
		NULL,
		'Standard'
	),
	(
		'c5e47b86-ee31-4e9c-a1c0-0c25241db073',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'080',
		'01.05.08.080',
		'Constructor’s submissions, reports and as-built documentation',
		NULL,
		'Standard'
	),
	(
		'6cf3f954-634b-4a95-8d3f-9e8b6003fb91',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'090',
		'01.05.08.090',
		'Quality monitoring, recording and inspections',
		NULL,
		'Standard'
	),
	(
		'c8651325-3d16-49b2-b248-f259f747c78d',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'100',
		'01.05.08.100',
		'Safety, health and environmental management',
		NULL,
		'Standard'
	),
	(
		'ee8cc381-14d9-4e24-b57f-d152a3f12602',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'110',
		'01.05.08.110',
		'Insurances, bonds, guarantees and warranties',
		NULL,
		'Standard'
	),
	(
		'7cc2e36c-14ff-4b56-aaed-c742ab756067',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'120',
		'01.05.08.120',
		'Constructor’s statutory fees and charges',
		NULL,
		'Standard'
	),
	(
		'b95cabbf-9300-4d6d-9bc2-613e8c665352',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'130',
		'01.05.08.130',
		'Testing and commissioning',
		NULL,
		'Standard'
	),
	(
		'd84a189a-9d46-4b8e-8032-8692374247d1',
		'02d82b80-93e4-41b5-a61b-876d78ccc271',
		icms_v3_library_id,
		4,
		'140',
		'01.05.08.140',
		'Extras for extreme climatic or working conditions (if priced separately according to local pricing practice)',
		NULL,
		'Standard'
	),
	(
		'08681a0e-ff5e-4ed2-aa5f-f57dadd4ba8a',
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		icms_v3_library_id,
		3,
		'09',
		'01.05.09',
		'Risk Allowances',
		'As defined in section 4.1 but related to [Construction | Renewal | Maintenance] Costs and not included in other Groups.',
		'Standard'
	),
	(
		'd92f5bd7-a408-484e-8021-48b67486979a',
		'08681a0e-ff5e-4ed2-aa5f-f57dadd4ba8a',
		icms_v3_library_id,
		4,
		'010',
		'01.05.09.010',
		'Design development allowance',
		NULL,
		'Standard'
	),
	(
		'238df245-5467-4260-a4b1-492570c50d52',
		'08681a0e-ff5e-4ed2-aa5f-f57dadd4ba8a',
		icms_v3_library_id,
		4,
		'020',
		'01.05.09.020',
		'Construction contingencies',
		NULL,
		'Standard'
	),
	(
		'6be2d89c-b799-4684-b9e0-3c3122591e71',
		'08681a0e-ff5e-4ed2-aa5f-f57dadd4ba8a',
		icms_v3_library_id,
		4,
		'030',
		'01.05.09.030',
		'Price Level Adjustments',
		NULL,
		'Standard'
	),
	(
		'a53ff660-dc26-4109-aa43-f48f90b389b0',
		'08681a0e-ff5e-4ed2-aa5f-f57dadd4ba8a',
		icms_v3_library_id,
		4,
		'040',
		'01.05.09.040',
		'Exchange rate fluctuation adjustments',
		NULL,
		'Standard'
	),
	(
		'beb7dade-bb9f-4aaa-a83b-46127595f924',
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		icms_v3_library_id,
		3,
		'10',
		'01.05.10',
		'Taxes and Levies',
		'As defined in section 4.1 and not included in other Groups.',
		'Standard'
	),
	(
		'00a60f1f-aad8-438a-ae7a-26f6a2a12740',
		'beb7dade-bb9f-4aaa-a83b-46127595f924',
		icms_v3_library_id,
		4,
		'010',
		'01.05.10.010',
		'Paid by the Constructor',
		NULL,
		'Standard'
	),
	(
		'362308f5-6356-4110-9e44-ff09fe6b006b',
		'beb7dade-bb9f-4aaa-a83b-46127595f924',
		icms_v3_library_id,
		4,
		'020',
		'01.05.10.020',
		'Paid by the Client in relation to the construction contract payments',
		NULL,
		'Standard'
	),
	(
		'35344fe9-5aff-4ec6-831f-a185e9d400d3',
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		icms_v3_library_id,
		3,
		'11',
		'01.05.11',
		'Work and utilities off-site',
		'All payments to government authorities or public utility companies to connect | keep connected public work and utilities to the site, or services diversions, to enable the Project, including related risk allowances, taxes and levies.',
		'Standard'
	),
	(
		'020bdc47-881b-447a-8aff-bbd8ceb96681',
		'35344fe9-5aff-4ec6-831f-a185e9d400d3',
		icms_v3_library_id,
		4,
		'010',
		'01.05.11.010',
		'Connections to, diversion of and capacity enhancement of public utility mains or sources off-site up to mains connections on-site',
		NULL,
		'Standard'
	),
	(
		'c8f85940-05bc-42df-8f5b-c716f52ecfce',
		'35344fe9-5aff-4ec6-831f-a185e9d400d3',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Public access roads and footpaths',
		NULL,
		'Standard'
	),
	(
		'e86c0f25-9e71-4f9e-af2d-209ac675e557',
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		icms_v3_library_id,
		3,
		'12',
		'01.05.12',
		'Production and loose furniture',
		' fittings and equipment',
		'Standard'
	),
	(
		'c3204bae-b9c1-4d8e-bcde-f69fcce70d9b',
		'e86c0f25-9e71-4f9e-af2d-209ac675e557',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Loose production, process and operating furniture, fittings and equipment not normally provided before completion of construction',
		NULL,
		'Standard'
	),
	(
		'e1cd7e87-9ee6-4207-a8dc-e30e2804255d',
		'e86c0f25-9e71-4f9e-af2d-209ac675e557',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Fixed production, process and operating furniture, fittings and equipment installed before completion of construction',
		NULL,
		'Standard'
	),
	(
		'31ac6dfa-00e1-4315-b2c9-372fce4d7715',
		'c9656708-6f2a-4aca-8160-05dca5c9fcd5',
		icms_v3_library_id,
		3,
		'13',
		'01.05.13',
		'Maintenance-related consultancies and supervision',
		'Fees and charges payable to Service Providers not engaged by the Constructors, including related risk allowances, taxes and levies.',
		'Standard'
	),
	(
		'47b72b6b-00cd-4ae1-a68b-2bf9ff9a82f9',
		'31ac6dfa-00e1-4315-b2c9-372fce4d7715',
		icms_v3_library_id,
		4,
		'010',
		'************',
		'Consultants’ fees and reimbursable',
		NULL,
		'Standard'
	),
	(
		'8089a3b6-5099-4c21-8a6d-3afd8a5ca004',
		'31ac6dfa-00e1-4315-b2c9-372fce4d7715',
		icms_v3_library_id,
		4,
		'020',
		'************',
		'Charges and levies payable to statutory bodies or their appointed agencies',
		NULL,
		'Standard'
	),
	(
		'f306d078-f9f4-4f14-9d4f-2e8a0482736c',
		'31ac6dfa-00e1-4315-b2c9-372fce4d7715',
		icms_v3_library_id,
		4,
		'030',
		'01.05.13.030',
		'Site supervision charges (including their accommodation and travels)',
		NULL,
		'Standard'
	),
	(
		'1a113dcd-ed5f-46d5-8549-258596925c4a',
		'31ac6dfa-00e1-4315-b2c9-372fce4d7715',
		icms_v3_library_id,
		4,
		'040',
		'01.05.13.040',
		'Payments to testing authorities or laboratories',
		NULL,
		'Standard'
	),
	(
		'640c7baa-6ab9-4604-8ce3-03542b0a1bc6',
		'c777ec64-2466-4d63-a315-2fb7a116403f',
		icms_v3_library_id,
		2,
		'06',
		'01.06',
		'End of Life Costs (EC)',
		'The net costs or fees for disposing of an asset at the end of its service life after deducting the salvage value and other income due to disposal, including costs resulting from disposal inspection, decommissioning and decontamination, demolition and reclamation, reinstatement, asset transfer obligations, recycling, recovery, disposal of components and materials, and transport and regulatory costs.',
		'Standard'
	),
	(
		'b8b33f32-5e14-4631-b7a9-96878897ef32',
		'640c7baa-6ab9-4604-8ce3-03542b0a1bc6',
		icms_v3_library_id,
		3,
		'01',
		'01.06.01',
		'Disposal inspection',
		'Inspections carried out in connection with demolition, dilapidations or other contractual requirements.',
		'Standard'
	),
	(
		'4abd4951-0bac-4c91-b9e5-7e0bd5a3dbe6',
		'b8b33f32-5e14-4631-b7a9-96878897ef32',
		icms_v3_library_id,
		4,
		'010',
		'01.06.01.010',
		'Dilapidations report',
		NULL,
		'Standard'
	),
	(
		'e422b9f6-f04c-4237-9a8a-5b061a9d2a8a',
		'b8b33f32-5e14-4631-b7a9-96878897ef32',
		icms_v3_library_id,
		4,
		'020',
		'01.06.01.020',
		'Contractual hand-back obligations',
		NULL,
		'Standard'
	),
	(
		'6c47058d-a807-4814-8108-d77e1b49674d',
		'640c7baa-6ab9-4604-8ce3-03542b0a1bc6',
		icms_v3_library_id,
		3,
		'02',
		'01.06.02',
		'Decommissioning and decontamination',
		'All post-occupation activities required to render the constructed asset ready for demolition.',
		'Standard'
	),
	(
		'952c4053-faa4-488d-bdbc-df9f9360b509',
		'6c47058d-a807-4814-8108-d77e1b49674d',
		icms_v3_library_id,
		4,
		'010',
		'01.06.02.010',
		'Shutdowns and decommissioning',
		NULL,
		'Standard'
	),
	(
		'50d8d47d-c448-4548-8acd-aeb9a50a242b',
		'6c47058d-a807-4814-8108-d77e1b49674d',
		icms_v3_library_id,
		4,
		'020',
		'01.06.02.020',
		'Decontamination',
		NULL,
		'Standard'
	),
	(
		'df5abb29-e2fa-4b6c-8245-bc88bd19b78f',
		'640c7baa-6ab9-4604-8ce3-03542b0a1bc6',
		icms_v3_library_id,
		3,
		'03',
		'01.06.03',
		'Demolition',
		' reclamation and salvage',
		'Standard'
	),
	(
		'3ecca1cd-0084-4199-ae68-314884f1c048',
		'df5abb29-e2fa-4b6c-8245-bc88bd19b78f',
		icms_v3_library_id,
		4,
		'010',
		'01.06.03.010',
		'Demolition',
		NULL,
		'Standard'
	),
	(
		'46fc68f2-3996-4871-bbb7-eeeac1ee0bfa',
		'df5abb29-e2fa-4b6c-8245-bc88bd19b78f',
		icms_v3_library_id,
		4,
		'020',
		'01.06.03.020',
		'Reclamation',
		NULL,
		'Standard'
	),
	(
		'487a74e3-2c0d-4533-8919-0c269e58032d',
		'df5abb29-e2fa-4b6c-8245-bc88bd19b78f',
		icms_v3_library_id,
		4,
		'030',
		'01.06.03.030',
		'Salvage',
		NULL,
		'Standard'
	),
	(
		'54e18f8c-4d83-4801-aac2-a8f214a66e64',
		'640c7baa-6ab9-4604-8ce3-03542b0a1bc6',
		icms_v3_library_id,
		3,
		'04',
		'01.06.04',
		'Reinstatement',
		'Dealing with dilapidations, measures to comply with other contractual obligations to return the constructed asset to a required standard of repair.',
		'Standard'
	),
	(
		'296a7777-7168-4281-a1ba-93887e899725',
		'54e18f8c-4d83-4801-aac2-a8f214a66e64',
		icms_v3_library_id,
		4,
		'010',
		'01.06.04.010',
		'Agreed reinstatement works',
		NULL,
		'Standard'
	),
	(
		'06d51ff1-ab61-40b2-a37f-5527703f988c',
		'54e18f8c-4d83-4801-aac2-a8f214a66e64',
		icms_v3_library_id,
		4,
		'020',
		'01.06.04.020',
		'Contractual obligations',
		NULL,
		'Standard'
	),
	(
		'8cf7565b-6d93-418c-9c4e-8f216d55c4ce',
		'640c7baa-6ab9-4604-8ce3-03542b0a1bc6',
		icms_v3_library_id,
		3,
		'05',
		'01.06.05',
		'Constructors’ site overheads | general requirements',
		'Constructors’ site management, temporary site facilities, site services, and expenses, not directly related to a particular Group, but commonly required to be shared by all Groups.',
		'Standard'
	),
	(
		'74f35b8e-9809-4e07-8916-0c89292a8e37',
		'8cf7565b-6d93-418c-9c4e-8f216d55c4ce',
		icms_v3_library_id,
		4,
		'010',
		'01.06.05.010',
		'Administration',
		NULL,
		'Standard'
	),
	(
		'64ed8606-ca86-4495-a077-261a7428f66a',
		'8cf7565b-6d93-418c-9c4e-8f216d55c4ce',
		icms_v3_library_id,
		4,
		'020',
		'01.06.05.020',
		'Overheads (project specific)',
		NULL,
		'Standard'
	),
	(
		'dec779d1-bc9a-4796-bb47-d10531d7cbaf',
		'640c7baa-6ab9-4604-8ce3-03542b0a1bc6',
		icms_v3_library_id,
		3,
		'06',
		'01.06.06',
		'Risk Allowances',
		'As defined in Part 4.1 but related to End of Life Costs and not included in other Groups.',
		'Standard'
	),
	(
		'5ae37763-f337-4f08-9e96-81d2185c6e2b',
		'dec779d1-bc9a-4796-bb47-d10531d7cbaf',
		icms_v3_library_id,
		4,
		'010',
		'01.06.06.010',
		'End of life specific (user definable)',
		NULL,
		'Standard'
	),
	(
		'0b0b5444-4737-4df0-b927-43b931f6cae7',
		'dec779d1-bc9a-4796-bb47-d10531d7cbaf',
		icms_v3_library_id,
		4,
		'020',
		'01.06.06.020',
		'Abnormal risks (user definable)',
		NULL,
		'Standard'
	),
	(
		'2a926382-e22a-49e6-9d75-0f8e786e85bb',
		'640c7baa-6ab9-4604-8ce3-03542b0a1bc6',
		icms_v3_library_id,
		3,
		'07',
		'01.06.07',
		'Taxes and Levies',
		'As defined in Part 4.1 but related to End of Life Costs.',
		'Standard'
	),
	(
		'24322752-4d68-4491-b747-f071ee34119a',
		'2a926382-e22a-49e6-9d75-0f8e786e85bb',
		icms_v3_library_id,
		4,
		'010',
		'01.06.07.010',
		'Taxes',
		NULL,
		'Standard'
	),
	(
		'87a6f205-0bc0-4239-859a-c3a6e3f880b8',
		'2a926382-e22a-49e6-9d75-0f8e786e85bb',
		icms_v3_library_id,
		4,
		'020',
		'01.06.07.020',
		'Levies',
		NULL,
		'Standard'
	),
	(
		'8fefbb66-67a6-4865-ae72-c4e9d622340c',
		'2a926382-e22a-49e6-9d75-0f8e786e85bb',
		icms_v3_library_id,
		4,
		'030',
		'01.06.07.030',
		'Credit for grants',
		NULL,
		'Standard'
	);

END $$;

COMMIT;
